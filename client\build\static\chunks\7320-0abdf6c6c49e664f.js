(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7320],{15116:function(e,t,i){"use strict";var r=i(32464),s=i(57437);t.Z=(0,r.Z)((0,s.jsx)("path",{d:"m17 7-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4z"}),"Logout")},84485:function(e,t,i){"use strict";var r=i(32464),s=i(57437);t.Z=(0,r.Z)((0,s.jsx)("path",{d:"M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6"}),"Settings")},67116:function(e,t,i){"use strict";i.d(t,{Z:function(){return x}});var r=i(2265),s=i(61994),n=i(20801),o=i(16210),a=i(76301),l=i(37053),u=i(32464),d=i(57437),p=(0,u.Z)((0,d.jsx)("path",{d:"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"}),"Person"),h=i(94143),c=i(50738);function g(e){return(0,c.ZP)("MuiAvatar",e)}(0,h.Z)("MuiAvatar",["root","colorDefault","circular","rounded","square","img","fallback"]);var f=i(79114);let m=e=>{let{classes:t,variant:i,colorDefault:r}=e;return(0,n.Z)({root:["root",i,r&&"colorDefault"],img:["img"],fallback:["fallback"]},g,t)},v=(0,o.ZP)("div",{name:"MuiAvatar",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,t[i.variant],i.colorDefault&&t.colorDefault]}})((0,a.Z)(e=>{let{theme:t}=e;return{position:"relative",display:"flex",alignItems:"center",justifyContent:"center",flexShrink:0,width:40,height:40,fontFamily:t.typography.fontFamily,fontSize:t.typography.pxToRem(20),lineHeight:1,borderRadius:"50%",overflow:"hidden",userSelect:"none",variants:[{props:{variant:"rounded"},style:{borderRadius:(t.vars||t).shape.borderRadius}},{props:{variant:"square"},style:{borderRadius:0}},{props:{colorDefault:!0},style:{color:(t.vars||t).palette.background.default,...t.vars?{backgroundColor:t.vars.palette.Avatar.defaultBg}:{backgroundColor:t.palette.grey[400],...t.applyStyles("dark",{backgroundColor:t.palette.grey[600]})}}}]}})),y=(0,o.ZP)("img",{name:"MuiAvatar",slot:"Img",overridesResolver:(e,t)=>t.img})({width:"100%",height:"100%",textAlign:"center",objectFit:"cover",color:"transparent",textIndent:1e4}),b=(0,o.ZP)(p,{name:"MuiAvatar",slot:"Fallback",overridesResolver:(e,t)=>t.fallback})({width:"75%",height:"75%"});var x=r.forwardRef(function(e,t){let i=(0,l.i)({props:e,name:"MuiAvatar"}),{alt:n,children:o,className:a,component:u="div",slots:p={},slotProps:h={},imgProps:c,sizes:g,src:x,srcSet:S,variant:k="circular",...w}=i,L=null,R={...i,component:u,variant:k},C=function(e){let{crossOrigin:t,referrerPolicy:i,src:s,srcSet:n}=e,[o,a]=r.useState(!1);return r.useEffect(()=>{if(!s&&!n)return;a(!1);let e=!0,r=new Image;return r.onload=()=>{e&&a("loaded")},r.onerror=()=>{e&&a("error")},r.crossOrigin=t,r.referrerPolicy=i,r.src=s,n&&(r.srcset=n),()=>{e=!1}},[t,i,s,n]),o}({...c,..."function"==typeof h.img?h.img(R):h.img,src:x,srcSet:S}),O=x||S,$=O&&"error"!==C;R.colorDefault=!$,delete R.ownerState;let P=m(R),[N,Z]=(0,f.Z)("img",{className:P.img,elementType:y,externalForwardedProps:{slots:p,slotProps:{img:{...c,...h.img}}},additionalProps:{alt:n,src:x,srcSet:S,sizes:g},ownerState:R});return L=$?(0,d.jsx)(N,{...Z}):o||0===o?o:O&&n?n[0]:(0,d.jsx)(b,{ownerState:R,className:P.fallback}),(0,d.jsx)(v,{as:u,className:(0,s.Z)(P.root,a),ref:t,...w,ownerState:R,children:L})})},17162:function(e,t,i){"use strict";i.d(t,{Z:function(){return L}});var r=i(2265),s=i(61994),n=i(52836),o=i(73207),a=i(20801),l=i(16210),u=i(31691),d=i(76301),p=i(37053),h=i(73220),c=i(31090),g=i(60118),f=i(94143),m=i(50738);function v(e){return(0,m.ZP)("MuiCollapse",e)}(0,f.Z)("MuiCollapse",["root","horizontal","vertical","entered","hidden","wrapper","wrapperInner"]);var y=i(57437);let b=e=>{let{orientation:t,classes:i}=e,r={root:["root",`${t}`],entered:["entered"],hidden:["hidden"],wrapper:["wrapper",`${t}`],wrapperInner:["wrapperInner",`${t}`]};return(0,a.Z)(r,v,i)},x=(0,l.ZP)("div",{name:"MuiCollapse",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,t[i.orientation],"entered"===i.state&&t.entered,"exited"===i.state&&!i.in&&"0px"===i.collapsedSize&&t.hidden]}})((0,d.Z)(e=>{let{theme:t}=e;return{height:0,overflow:"hidden",transition:t.transitions.create("height"),variants:[{props:{orientation:"horizontal"},style:{height:"auto",width:0,transition:t.transitions.create("width")}},{props:{state:"entered"},style:{height:"auto",overflow:"visible"}},{props:{state:"entered",orientation:"horizontal"},style:{width:"auto"}},{props:e=>{let{ownerState:t}=e;return"exited"===t.state&&!t.in&&"0px"===t.collapsedSize},style:{visibility:"hidden"}}]}})),S=(0,l.ZP)("div",{name:"MuiCollapse",slot:"Wrapper",overridesResolver:(e,t)=>t.wrapper})({display:"flex",width:"100%",variants:[{props:{orientation:"horizontal"},style:{width:"auto",height:"100%"}}]}),k=(0,l.ZP)("div",{name:"MuiCollapse",slot:"WrapperInner",overridesResolver:(e,t)=>t.wrapperInner})({width:"100%",variants:[{props:{orientation:"horizontal"},style:{width:"auto",height:"100%"}}]}),w=r.forwardRef(function(e,t){let i=(0,p.i)({props:e,name:"MuiCollapse"}),{addEndListener:a,children:l,className:d,collapsedSize:f="0px",component:m,easing:v,in:w,onEnter:L,onEntered:R,onEntering:C,onExit:O,onExited:$,onExiting:P,orientation:N="vertical",style:Z,timeout:I=h.x9.standard,TransitionComponent:j=n.ZP,...A}=i,M={...i,orientation:N,collapsedSize:f},F=b(M),E=(0,u.Z)(),D=(0,o.Z)(),T=r.useRef(null),V=r.useRef(),z="number"==typeof f?`${f}px`:f,U="horizontal"===N,B=U?"width":"height",H=r.useRef(null),W=(0,g.Z)(t,H),K=e=>t=>{if(e){let i=H.current;void 0===t?e(i):e(i,t)}},J=()=>T.current?T.current[U?"clientWidth":"clientHeight"]:0,G=K((e,t)=>{T.current&&U&&(T.current.style.position="absolute"),e.style[B]=z,L&&L(e,t)}),q=K((e,t)=>{let i=J();T.current&&U&&(T.current.style.position="");let{duration:r,easing:s}=(0,c.C)({style:Z,timeout:I,easing:v},{mode:"enter"});if("auto"===I){let t=E.transitions.getAutoHeightDuration(i);e.style.transitionDuration=`${t}ms`,V.current=t}else e.style.transitionDuration="string"==typeof r?r:`${r}ms`;e.style[B]=`${i}px`,e.style.transitionTimingFunction=s,C&&C(e,t)}),_=K((e,t)=>{e.style[B]="auto",R&&R(e,t)}),Y=K(e=>{e.style[B]=`${J()}px`,O&&O(e)}),Q=K($),X=K(e=>{let t=J(),{duration:i,easing:r}=(0,c.C)({style:Z,timeout:I,easing:v},{mode:"exit"});if("auto"===I){let i=E.transitions.getAutoHeightDuration(t);e.style.transitionDuration=`${i}ms`,V.current=i}else e.style.transitionDuration="string"==typeof i?i:`${i}ms`;e.style[B]=z,e.style.transitionTimingFunction=r,P&&P(e)});return(0,y.jsx)(j,{in:w,onEnter:G,onEntered:_,onEntering:q,onExit:Y,onExited:Q,onExiting:X,addEndListener:e=>{"auto"===I&&D.start(V.current||0,e),a&&a(H.current,e)},nodeRef:H,timeout:"auto"===I?null:I,...A,children:(e,t)=>{let{ownerState:i,...r}=t;return(0,y.jsx)(x,{as:m,className:(0,s.Z)(F.root,d,{entered:F.entered,exited:!w&&"0px"===z&&F.hidden}[e]),style:{[U?"minWidth":"minHeight"]:z,...Z},ref:W,ownerState:{...M,state:e},...r,children:(0,y.jsx)(S,{ownerState:{...M,state:e},className:F.wrapper,ref:T,children:(0,y.jsx)(k,{ownerState:{...M,state:e},className:F.wrapperInner,children:l})})})}})});w&&(w.muiSupportAuto=!0);var L=w},98489:function(e,t,i){"use strict";i.d(t,{default:function(){return b}});var r=i(2265),s=i(61994),n=i(50738),o=i(20801),a=i(4647),l=i(20956),u=i(95045),d=i(58698),p=i(57437);let h=(0,d.Z)(),c=(0,u.Z)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,t[`maxWidth${(0,a.Z)(String(i.maxWidth))}`],i.fixed&&t.fixed,i.disableGutters&&t.disableGutters]}}),g=e=>(0,l.Z)({props:e,name:"MuiContainer",defaultTheme:h}),f=(e,t)=>{let{classes:i,fixed:r,disableGutters:s,maxWidth:l}=e,u={root:["root",l&&`maxWidth${(0,a.Z)(String(l))}`,r&&"fixed",s&&"disableGutters"]};return(0,o.Z)(u,e=>(0,n.ZP)(t,e),i)};var m=i(85657),v=i(16210),y=i(37053),b=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{createStyledComponent:t=c,useThemeProps:i=g,componentName:n="MuiContainer"}=e,o=t(e=>{let{theme:t,ownerState:i}=e;return{width:"100%",marginLeft:"auto",boxSizing:"border-box",marginRight:"auto",...!i.disableGutters&&{paddingLeft:t.spacing(2),paddingRight:t.spacing(2),[t.breakpoints.up("sm")]:{paddingLeft:t.spacing(3),paddingRight:t.spacing(3)}}}},e=>{let{theme:t,ownerState:i}=e;return i.fixed&&Object.keys(t.breakpoints.values).reduce((e,i)=>{let r=t.breakpoints.values[i];return 0!==r&&(e[t.breakpoints.up(i)]={maxWidth:`${r}${t.breakpoints.unit}`}),e},{})},e=>{let{theme:t,ownerState:i}=e;return{..."xs"===i.maxWidth&&{[t.breakpoints.up("xs")]:{maxWidth:Math.max(t.breakpoints.values.xs,444)}},...i.maxWidth&&"xs"!==i.maxWidth&&{[t.breakpoints.up(i.maxWidth)]:{maxWidth:`${t.breakpoints.values[i.maxWidth]}${t.breakpoints.unit}`}}}});return r.forwardRef(function(e,t){let r=i(e),{className:a,component:l="div",disableGutters:u=!1,fixed:d=!1,maxWidth:h="lg",classes:c,...g}=r,m={...r,component:l,disableGutters:u,fixed:d,maxWidth:h},v=f(m,n);return(0,p.jsx)(o,{as:l,ownerState:m,className:(0,s.Z)(v.root,a),ref:t,...g})})}({createStyledComponent:(0,v.ZP)("div",{name:"MuiContainer",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,t[`maxWidth${(0,m.Z)(String(i.maxWidth))}`],i.fixed&&t.fixed,i.disableGutters&&t.disableGutters]}}),useThemeProps:e=>(0,y.i)({props:e,name:"MuiContainer"})})},8350:function(e,t,i){"use strict";var r=i(2265),s=i(61994),n=i(20801),o=i(82590),a=i(16210),l=i(76301),u=i(37053),d=i(42596),p=i(57437);let h=e=>{let{absolute:t,children:i,classes:r,flexItem:s,light:o,orientation:a,textAlign:l,variant:u}=e;return(0,n.Z)({root:["root",t&&"absolute",u,o&&"light","vertical"===a&&"vertical",s&&"flexItem",i&&"withChildren",i&&"vertical"===a&&"withChildrenVertical","right"===l&&"vertical"!==a&&"textAlignRight","left"===l&&"vertical"!==a&&"textAlignLeft"],wrapper:["wrapper","vertical"===a&&"wrapperVertical"]},d.V,r)},c=(0,a.ZP)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,i.absolute&&t.absolute,t[i.variant],i.light&&t.light,"vertical"===i.orientation&&t.vertical,i.flexItem&&t.flexItem,i.children&&t.withChildren,i.children&&"vertical"===i.orientation&&t.withChildrenVertical,"right"===i.textAlign&&"vertical"!==i.orientation&&t.textAlignRight,"left"===i.textAlign&&"vertical"!==i.orientation&&t.textAlignLeft]}})((0,l.Z)(e=>{let{theme:t}=e;return{margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(t.vars||t).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:t.vars?`rgba(${t.vars.palette.dividerChannel} / 0.08)`:(0,o.Fq)(t.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:t.spacing(2),marginRight:t.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:t.spacing(1),marginBottom:t.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:e=>{let{ownerState:t}=e;return!!t.children},style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:e=>{let{ownerState:t}=e;return t.children&&"vertical"!==t.orientation},style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(t.vars||t).palette.divider}`,borderTopStyle:"inherit"}}},{props:e=>{let{ownerState:t}=e;return"vertical"===t.orientation&&t.children},style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(t.vars||t).palette.divider}`,borderLeftStyle:"inherit"}}},{props:e=>{let{ownerState:t}=e;return"right"===t.textAlign&&"vertical"!==t.orientation},style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:e=>{let{ownerState:t}=e;return"left"===t.textAlign&&"vertical"!==t.orientation},style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}})),g=(0,a.ZP)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.wrapper,"vertical"===i.orientation&&t.wrapperVertical]}})((0,l.Z)(e=>{let{theme:t}=e;return{display:"inline-block",paddingLeft:`calc(${t.spacing(1)} * 1.2)`,paddingRight:`calc(${t.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${t.spacing(1)} * 1.2)`,paddingBottom:`calc(${t.spacing(1)} * 1.2)`}}]}})),f=r.forwardRef(function(e,t){let i=(0,u.i)({props:e,name:"MuiDivider"}),{absolute:r=!1,children:n,className:o,orientation:a="horizontal",component:l=n||"vertical"===a?"div":"hr",flexItem:d=!1,light:f=!1,role:m="hr"!==l?"separator":void 0,textAlign:v="center",variant:y="fullWidth",...b}=i,x={...i,absolute:r,component:l,flexItem:d,light:f,orientation:a,role:m,textAlign:v,variant:y},S=h(x);return(0,p.jsx)(c,{as:l,className:(0,s.Z)(S.root,o),role:m,ref:t,ownerState:x,"aria-orientation":"separator"===m&&("hr"!==l||"vertical"===a)?a:void 0,...b,children:n?(0,p.jsx)(g,{className:S.wrapper,ownerState:x,children:n}):null})});f&&(f.muiSkipListHighlight=!0),t.Z=f},11741:function(e,t,i){"use strict";var r=i(2265),s=i(61994),n=i(20801),o=i(82590),a=i(16210),l=i(76301),u=i(37053),d=i(34765),p=i(82662),h=i(84217),c=i(60118),g=i(15566),f=i(14836),m=i(57437);let v=e=>{let{alignItems:t,classes:i,dense:r,disabled:s,disableGutters:o,divider:a,selected:l}=e,u=(0,n.Z)({root:["root",r&&"dense",!o&&"gutters",a&&"divider",s&&"disabled","flex-start"===t&&"alignItemsFlexStart",l&&"selected"]},f.t,i);return{...i,...u}},y=(0,a.ZP)(p.Z,{shouldForwardProp:e=>(0,d.Z)(e)||"classes"===e,name:"MuiListItemButton",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,i.dense&&t.dense,"flex-start"===i.alignItems&&t.alignItemsFlexStart,i.divider&&t.divider,!i.disableGutters&&t.gutters]}})((0,l.Z)(e=>{let{theme:t}=e;return{display:"flex",flexGrow:1,justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minWidth:0,boxSizing:"border-box",textAlign:"left",paddingTop:8,paddingBottom:8,transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${f.Z.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,o.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${f.Z.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,o.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${f.Z.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,o.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,o.Fq)(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${f.Z.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${f.Z.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity},variants:[{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"}},{props:{alignItems:"flex-start"},style:{alignItems:"flex-start"}},{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.dense},style:{paddingTop:4,paddingBottom:4}}]}})),b=r.forwardRef(function(e,t){let i=(0,u.i)({props:e,name:"MuiListItemButton"}),{alignItems:n="center",autoFocus:o=!1,component:a="div",children:l,dense:d=!1,disableGutters:p=!1,divider:f=!1,focusVisibleClassName:b,selected:x=!1,className:S,...k}=i,w=r.useContext(g.Z),L=r.useMemo(()=>({dense:d||w.dense||!1,alignItems:n,disableGutters:p}),[n,w.dense,d,p]),R=r.useRef(null);(0,h.Z)(()=>{o&&R.current&&R.current.focus()},[o]);let C={...i,alignItems:n,dense:L.dense,disableGutters:p,divider:f,selected:x},O=v(C),$=(0,c.Z)(R,t);return(0,m.jsx)(g.Z.Provider,{value:L,children:(0,m.jsx)(y,{ref:$,href:k.href||k.to,component:(k.href||k.to)&&"div"===a?"button":a,focusVisibleClassName:(0,s.Z)(O.focusVisible,b),ownerState:C,className:(0,s.Z)(O.root,S),...k,classes:O,children:l})})});t.Z=b},14836:function(e,t,i){"use strict";i.d(t,{t:function(){return n}});var r=i(94143),s=i(50738);function n(e){return(0,s.ZP)("MuiListItemButton",e)}let o=(0,r.Z)("MuiListItemButton",["root","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","selected"]);t.Z=o},53431:function(e,t,i){"use strict";var r=i(2265),s=i(61994),n=i(20801),o=i(16210),a=i(76301),l=i(37053),u=i(67752),d=i(15566),p=i(57437);let h=e=>{let{alignItems:t,classes:i}=e;return(0,n.Z)({root:["root","flex-start"===t&&"alignItemsFlexStart"]},u.f,i)},c=(0,o.ZP)("div",{name:"MuiListItemIcon",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,"flex-start"===i.alignItems&&t.alignItemsFlexStart]}})((0,a.Z)(e=>{let{theme:t}=e;return{minWidth:56,color:(t.vars||t).palette.action.active,flexShrink:0,display:"inline-flex",variants:[{props:{alignItems:"flex-start"},style:{marginTop:8}}]}})),g=r.forwardRef(function(e,t){let i=(0,l.i)({props:e,name:"MuiListItemIcon"}),{className:n,...o}=i,a=r.useContext(d.Z),u={...i,alignItems:a.alignItems},g=h(u);return(0,p.jsx)(c,{className:(0,s.Z)(g.root,n),ownerState:u,ref:t,...o})});t.Z=g},67051:function(e,t,i){"use strict";var r=i(2265),s=i(61994),n=i(20801),o=i(56200),a=i(46387),l=i(15566),u=i(16210),d=i(37053),p=i(3127),h=i(79114),c=i(57437);let g=e=>{let{classes:t,inset:i,primary:r,secondary:s,dense:o}=e;return(0,n.Z)({root:["root",i&&"inset",o&&"dense",r&&s&&"multiline"],primary:["primary"],secondary:["secondary"]},p.L,t)},f=(0,u.ZP)("div",{name:"MuiListItemText",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[{[`& .${p.Z.primary}`]:t.primary},{[`& .${p.Z.secondary}`]:t.secondary},t.root,i.inset&&t.inset,i.primary&&i.secondary&&t.multiline,i.dense&&t.dense]}})({flex:"1 1 auto",minWidth:0,marginTop:4,marginBottom:4,[`.${o.Z.root}:where(& .${p.Z.primary})`]:{display:"block"},[`.${o.Z.root}:where(& .${p.Z.secondary})`]:{display:"block"},variants:[{props:e=>{let{ownerState:t}=e;return t.primary&&t.secondary},style:{marginTop:6,marginBottom:6}},{props:e=>{let{ownerState:t}=e;return t.inset},style:{paddingLeft:56}}]}),m=r.forwardRef(function(e,t){let i=(0,d.i)({props:e,name:"MuiListItemText"}),{children:n,className:o,disableTypography:u=!1,inset:p=!1,primary:m,primaryTypographyProps:v,secondary:y,secondaryTypographyProps:b,slots:x={},slotProps:S={},...k}=i,{dense:w}=r.useContext(l.Z),L=null!=m?m:n,R=y,C={...i,disableTypography:u,inset:p,primary:!!L,secondary:!!R,dense:w},O=g(C),$={slots:x,slotProps:{primary:v,secondary:b,...S}},[P,N]=(0,h.Z)("root",{className:(0,s.Z)(O.root,o),elementType:f,externalForwardedProps:{...$,...k},ownerState:C,ref:t}),[Z,I]=(0,h.Z)("primary",{className:O.primary,elementType:a.default,externalForwardedProps:$,ownerState:C}),[j,A]=(0,h.Z)("secondary",{className:O.secondary,elementType:a.default,externalForwardedProps:$,ownerState:C});return null==L||L.type===a.default||u||(L=(0,c.jsx)(Z,{variant:w?"body2":"body1",component:I?.variant?void 0:"span",...I,children:L})),null==R||R.type===a.default||u||(R=(0,c.jsx)(j,{variant:"body2",color:"textSecondary",...A,children:R})),(0,c.jsxs)(P,{...N,children:[L,R]})});t.Z=m},73261:function(e,t,i){"use strict";i.d(t,{ZP:function(){return R}});var r=i(2265),s=i(61994),n=i(20801),o=i(80022),a=i(16210),l=i(76301),u=i(37053),d=i(93513),p=i(60118),h=i(15566),c=i(94143),g=i(50738);function f(e){return(0,g.ZP)("MuiListItem",e)}(0,c.Z)("MuiListItem",["root","container","dense","alignItemsFlexStart","divider","gutters","padding","secondaryAction"]);var m=i(14836);function v(e){return(0,g.ZP)("MuiListItemSecondaryAction",e)}(0,c.Z)("MuiListItemSecondaryAction",["root","disableGutters"]);var y=i(57437);let b=e=>{let{disableGutters:t,classes:i}=e;return(0,n.Z)({root:["root",t&&"disableGutters"]},v,i)},x=(0,a.ZP)("div",{name:"MuiListItemSecondaryAction",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,i.disableGutters&&t.disableGutters]}})({position:"absolute",right:16,top:"50%",transform:"translateY(-50%)",variants:[{props:e=>{let{ownerState:t}=e;return t.disableGutters},style:{right:0}}]}),S=r.forwardRef(function(e,t){let i=(0,u.i)({props:e,name:"MuiListItemSecondaryAction"}),{className:n,...o}=i,a=r.useContext(h.Z),l={...i,disableGutters:a.disableGutters},d=b(l);return(0,y.jsx)(x,{className:(0,s.Z)(d.root,n),ownerState:l,ref:t,...o})});S.muiName="ListItemSecondaryAction";let k=e=>{let{alignItems:t,classes:i,dense:r,disableGutters:s,disablePadding:o,divider:a,hasSecondaryAction:l}=e;return(0,n.Z)({root:["root",r&&"dense",!s&&"gutters",!o&&"padding",a&&"divider","flex-start"===t&&"alignItemsFlexStart",l&&"secondaryAction"],container:["container"]},f,i)},w=(0,a.ZP)("div",{name:"MuiListItem",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:i}=e;return[t.root,i.dense&&t.dense,"flex-start"===i.alignItems&&t.alignItemsFlexStart,i.divider&&t.divider,!i.disableGutters&&t.gutters,!i.disablePadding&&t.padding,i.hasSecondaryAction&&t.secondaryAction]}})((0,l.Z)(e=>{let{theme:t}=e;return{display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",width:"100%",boxSizing:"border-box",textAlign:"left",variants:[{props:e=>{let{ownerState:t}=e;return!t.disablePadding},style:{paddingTop:8,paddingBottom:8}},{props:e=>{let{ownerState:t}=e;return!t.disablePadding&&t.dense},style:{paddingTop:4,paddingBottom:4}},{props:e=>{let{ownerState:t}=e;return!t.disablePadding&&!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return!t.disablePadding&&!!t.secondaryAction},style:{paddingRight:48}},{props:e=>{let{ownerState:t}=e;return!!t.secondaryAction},style:{[`& > .${m.Z.root}`]:{paddingRight:48}}},{props:{alignItems:"flex-start"},style:{alignItems:"flex-start"}},{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"}},{props:e=>{let{ownerState:t}=e;return t.button},style:{transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}}}},{props:e=>{let{ownerState:t}=e;return t.hasSecondaryAction},style:{paddingRight:48}}]}})),L=(0,a.ZP)("li",{name:"MuiListItem",slot:"Container",overridesResolver:(e,t)=>t.container})({position:"relative"});var R=r.forwardRef(function(e,t){let i=(0,u.i)({props:e,name:"MuiListItem"}),{alignItems:n="center",children:a,className:l,component:c,components:g={},componentsProps:f={},ContainerComponent:m="li",ContainerProps:{className:v,...b}={},dense:x=!1,disableGutters:R=!1,disablePadding:C=!1,divider:O=!1,secondaryAction:$,slotProps:P={},slots:N={},...Z}=i,I=r.useContext(h.Z),j=r.useMemo(()=>({dense:x||I.dense||!1,alignItems:n,disableGutters:R}),[n,I.dense,x,R]),A=r.useRef(null),M=r.Children.toArray(a),F=M.length&&(0,d.Z)(M[M.length-1],["ListItemSecondaryAction"]),E={...i,alignItems:n,dense:j.dense,disableGutters:R,disablePadding:C,divider:O,hasSecondaryAction:F},D=k(E),T=(0,p.Z)(A,t),V=N.root||g.Root||w,z=P.root||f.root||{},U={className:(0,s.Z)(D.root,z.className,l),...Z},B=c||"li";return F?(B=U.component||c?B:"div","li"===m&&("li"===B?B="div":"li"===U.component&&(U.component="div")),(0,y.jsx)(h.Z.Provider,{value:j,children:(0,y.jsxs)(L,{as:m,className:(0,s.Z)(D.container,v),ref:T,ownerState:E,...b,children:[(0,y.jsx)(V,{...z,...!(0,o.Z)(V)&&{as:B,ownerState:{...E,...z.ownerState}},...U,children:M}),M.pop()]})})):(0,y.jsx)(h.Z.Provider,{value:j,children:(0,y.jsxs)(V,{...z,as:B,ref:T,...!(0,o.Z)(V)&&{ownerState:{...E,...z.ownerState}},...U,children:[M,$&&(0,y.jsx)(S,{children:$})]})})})},95045:function(e,t,i){"use strict";let r=(0,i(29418).ZP)();t.Z=r},93826:function(e,t,i){"use strict";i.d(t,{Z:function(){return s}});var r=i(53232);function s(e){let{theme:t,name:i,props:s}=e;return t&&t.components&&t.components[i]&&t.components[i].defaultProps?(0,r.Z)(t.components[i].defaultProps,s):s}},20956:function(e,t,i){"use strict";i.d(t,{Z:function(){return n}});var r=i(93826),s=i(49695);function n(e){let{props:t,name:i,defaultTheme:n,themeId:o}=e,a=(0,s.Z)(n);return o&&(a=a[o]||a),(0,r.Z)({theme:a,name:i,props:t})}},59873:function(e,t,i){"use strict";i.d(t,{Z:function(){return d}});var r=i(2265),s=i.t(r,2),n=i(3450),o=i(93826),a=i(42827);let l={...s}.useSyncExternalStore;function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{themeId:t}=e;return function(e){let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=(0,a.Z)();s&&t&&(s=s[t]||s);let u="undefined"!=typeof window&&void 0!==window.matchMedia,{defaultMatches:d=!1,matchMedia:p=u?window.matchMedia:null,ssrMatchMedia:h=null,noSsr:c=!1}=(0,o.Z)({name:"MuiUseMediaQuery",props:i,theme:s}),g="function"==typeof e?e(s):e;return(void 0!==l?function(e,t,i,s,n){let o=r.useCallback(()=>t,[t]),a=r.useMemo(()=>{if(n&&i)return()=>i(e).matches;if(null!==s){let{matches:t}=s(e);return()=>t}return o},[o,e,s,n,i]),[u,d]=r.useMemo(()=>{if(null===i)return[o,()=>()=>{}];let t=i(e);return[()=>t.matches,e=>(t.addEventListener("change",e),()=>{t.removeEventListener("change",e)})]},[o,i,e]);return l(d,u,a)}:function(e,t,i,s,o){let[a,l]=r.useState(()=>o&&i?i(e).matches:s?s(e).matches:t);return(0,n.Z)(()=>{if(!i)return;let t=i(e),r=()=>{l(t.matches)};return r(),t.addEventListener("change",r),()=>{t.removeEventListener("change",r)}},[e,i]),a})(g=g.replace(/^@media( ?)/m,""),d,p,h,c)}}u();var d=u({themeId:i(22166).Z})},33145:function(e,t,i){"use strict";i.d(t,{default:function(){return s.a}});var r=i(48461),s=i.n(r)},99376:function(e,t,i){"use strict";var r=i(35475);i.o(r,"redirect")&&i.d(t,{redirect:function(){return r.redirect}}),i.o(r,"usePathname")&&i.d(t,{usePathname:function(){return r.usePathname}}),i.o(r,"useRouter")&&i.d(t,{useRouter:function(){return r.useRouter}}),i.o(r,"useSearchParams")&&i.d(t,{useSearchParams:function(){return r.useSearchParams}})},48461:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return a}});let r=i(47043),s=i(55346),n=i(65878),o=r._(i(5084));function a(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image/",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=n.Image},2648:function(){},740:function(e,t,i){"use strict";i.d(t,{Z:function(){return r}});var r=function(e){return{type:"backend",init:function(e,t,i){},read:function(t,i,r){if("function"==typeof e){if(e.length<3){try{var s=e(t,i);s&&"function"==typeof s.then?s.then(function(e){return r(null,e&&e.default||e)}).catch(r):r(null,s)}catch(e){r(e)}return}e(t,i,r);return}r(null,e&&e[t]&&e[t][i])}}}},46550:function(e,t,i){"use strict";i.d(t,{Fs:function(){return ei}});let r=e=>"string"==typeof e,s=()=>{let e,t;let i=new Promise((i,r)=>{e=i,t=r});return i.resolve=e,i.reject=t,i},n=e=>null==e?"":""+e,o=(e,t,i)=>{e.forEach(e=>{t[e]&&(i[e]=t[e])})},a=/###/g,l=e=>e&&e.indexOf("###")>-1?e.replace(a,"."):e,u=e=>!e||r(e),d=(e,t,i)=>{let s=r(t)?t.split("."):t,n=0;for(;n<s.length-1;){if(u(e))return{};let t=l(s[n]);!e[t]&&i&&(e[t]=new i),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++n}return u(e)?{}:{obj:e,k:l(s[n])}},p=(e,t,i)=>{let{obj:r,k:s}=d(e,t,Object);if(void 0!==r||1===t.length){r[s]=i;return}let n=t[t.length-1],o=t.slice(0,t.length-1),a=d(e,o,Object);for(;void 0===a.obj&&o.length;)n=`${o[o.length-1]}.${n}`,(a=d(e,o=o.slice(0,o.length-1),Object))&&a.obj&&void 0!==a.obj[`${a.k}.${n}`]&&(a.obj=void 0);a.obj[`${a.k}.${n}`]=i},h=(e,t,i,r)=>{let{obj:s,k:n}=d(e,t,Object);s[n]=s[n]||[],s[n].push(i)},c=(e,t)=>{let{obj:i,k:r}=d(e,t);if(i)return i[r]},g=(e,t,i)=>{let r=c(e,i);return void 0!==r?r:c(t,i)},f=(e,t,i)=>{for(let s in t)"__proto__"!==s&&"constructor"!==s&&(s in e?r(e[s])||e[s]instanceof String||r(t[s])||t[s]instanceof String?i&&(e[s]=t[s]):f(e[s],t[s],i):e[s]=t[s]);return e},m=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var v={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};let y=e=>r(e)?e.replace(/[&<>"'\/]/g,e=>v[e]):e;class b{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(void 0!==t)return t;let i=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,i),this.regExpQueue.push(e),i}}let x=[" ",",","?","!",";"],S=new b(20),k=(e,t,i)=>{t=t||"",i=i||"";let r=x.filter(e=>0>t.indexOf(e)&&0>i.indexOf(e));if(0===r.length)return!0;let s=S.getRegExp(`(${r.map(e=>"?"===e?"\\?":e).join("|")})`),n=!s.test(e);if(!n){let t=e.indexOf(i);t>0&&!s.test(e.substring(0,t))&&(n=!0)}return n},w=function(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];let r=t.split(i),s=e;for(let e=0;e<r.length;){let t;if(!s||"object"!=typeof s)return;let n="";for(let o=e;o<r.length;++o)if(o!==e&&(n+=i),n+=r[o],void 0!==(t=s[n])){if(["string","number","boolean"].indexOf(typeof t)>-1&&o<r.length-1)continue;e+=o-e+1;break}s=t}return s},L=e=>e&&e.replace("_","-"),R={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class C{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||R,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,i,s){return s&&!this.debug?null:(r(e[0])&&(e[0]=`${i}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new C(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new C(this.logger,e)}}var O=new C;class ${constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(e=>{this.observers[e]||(this.observers[e]=new Map);let i=this.observers[e].get(t)||0;this.observers[e].set(t,i+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(e=>{let[t,r]=e;for(let e=0;e<r;e++)t(...i)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(t=>{let[r,s]=t;for(let t=0;t<s;t++)r.apply(r,[e,...i])})}}class P extends ${constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,i){let s,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=void 0!==n.keySeparator?n.keySeparator:this.options.keySeparator,a=void 0!==n.ignoreJSONStructure?n.ignoreJSONStructure:this.options.ignoreJSONStructure;e.indexOf(".")>-1?s=e.split("."):(s=[e,t],i&&(Array.isArray(i)?s.push(...i):r(i)&&o?s.push(...i.split(o)):s.push(i)));let l=c(this.data,s);return(!l&&!t&&!i&&e.indexOf(".")>-1&&(e=s[0],t=s[1],i=s.slice(2).join(".")),!l&&a&&r(i))?w(this.data&&this.data[e]&&this.data[e][t],i,o):l}addResource(e,t,i,r){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1},n=void 0!==s.keySeparator?s.keySeparator:this.options.keySeparator,o=[e,t];i&&(o=o.concat(n?i.split(n):i)),e.indexOf(".")>-1&&(o=e.split("."),r=t,t=o[1]),this.addNamespaces(t),p(this.data,o,r),s.silent||this.emit("added",e,t,i,r)}addResources(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(let s in i)(r(i[s])||Array.isArray(i[s]))&&this.addResource(e,t,s,i[s],{silent:!0});s.silent||this.emit("added",e,t,i)}addResourceBundle(e,t,i,r,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},o=[e,t];e.indexOf(".")>-1&&(o=e.split("."),r=i,i=t,t=o[1]),this.addNamespaces(t);let a=c(this.data,o)||{};n.skipCopy||(i=JSON.parse(JSON.stringify(i))),r?f(a,i,s):a={...a,...i},p(this.data,o,a),n.silent||this.emit("added",e,t,i)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return(t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI)?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(e=>t[e]&&Object.keys(t[e]).length>0)}toJSON(){return this.data}}var N={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,i,r,s){return e.forEach(e=>{this.processors[e]&&(t=this.processors[e].process(t,i,r,s))}),t}};let Z={};class I extends ${constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),o(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=O.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;let i=this.resolve(e,t);return i&&void 0!==i.res}extractFromKey(e,t){let i=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===i&&(i=":");let s=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,n=t.ns||this.options.defaultNS||[],o=i&&e.indexOf(i)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!k(e,i,s);if(o&&!a){let t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:r(n)?[n]:n};let o=e.split(i);(i!==s||i===s&&this.options.ns.indexOf(o[0])>-1)&&(n=o.shift()),e=o.join(s)}return{key:e,namespaces:r(n)?[n]:n}}translate(e,t,i){if("object"!=typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof t&&(t={...t}),t||(t={}),null==e)return"";Array.isArray(e)||(e=[String(e)]);let s=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,n=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:o,namespaces:a}=this.extractFromKey(e[e.length-1],t),l=a[a.length-1],u=t.lng||this.language,d=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(u&&"cimode"===u.toLowerCase()){if(d){let e=t.nsSeparator||this.options.nsSeparator;return s?{res:`${l}${e}${o}`,usedKey:o,exactUsedKey:o,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:`${l}${e}${o}`}return s?{res:o,usedKey:o,exactUsedKey:o,usedLng:u,usedNS:l,usedParams:this.getUsedParamsDetails(t)}:o}let p=this.resolve(e,t),h=p&&p.res,c=p&&p.usedKey||o,g=p&&p.exactUsedKey||o,f=Object.prototype.toString.apply(h),m=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,v=!this.i18nFormat||this.i18nFormat.handleAsObject,y=!r(h)&&"boolean"!=typeof h&&"number"!=typeof h;if(v&&h&&y&&0>["[object Number]","[object Function]","[object RegExp]"].indexOf(f)&&!(r(m)&&Array.isArray(h))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(c,h,{...t,ns:a}):`key '${o} (${this.language})' returned an object instead of string.`;return s?(p.res=e,p.usedParams=this.getUsedParamsDetails(t),p):e}if(n){let e=Array.isArray(h),i=e?[]:{},r=e?g:c;for(let e in h)if(Object.prototype.hasOwnProperty.call(h,e)){let s=`${r}${n}${e}`;i[e]=this.translate(s,{...t,joinArrays:!1,ns:a}),i[e]===s&&(i[e]=h[e])}h=i}}else if(v&&r(m)&&Array.isArray(h))(h=h.join(m))&&(h=this.extendTranslation(h,e,t,i));else{let s=!1,a=!1,d=void 0!==t.count&&!r(t.count),c=I.hasDefaultValue(t),g=d?this.pluralResolver.getSuffix(u,t.count,t):"",f=t.ordinal&&d?this.pluralResolver.getSuffix(u,t.count,{ordinal:!1}):"",m=d&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),v=m&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${g}`]||t[`defaultValue${f}`]||t.defaultValue;!this.isValidLookup(h)&&c&&(s=!0,h=v),this.isValidLookup(h)||(a=!0,h=o);let y=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&a?void 0:h,b=c&&v!==h&&this.options.updateMissing;if(a||s||b){if(this.logger.log(b?"updateKey":"missingKey",u,l,o,b?v:h),n){let e=this.resolve(o,{...t,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[],i=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&i&&i[0])for(let t=0;t<i.length;t++)e.push(i[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);let r=(e,i,r)=>{let s=c&&r!==h?r:y;this.options.missingKeyHandler?this.options.missingKeyHandler(e,l,i,s,b,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,l,i,s,b,t),this.emit("missingKey",e,l,i,h)};this.options.saveMissing&&(this.options.saveMissingPlurals&&d?e.forEach(e=>{let i=this.pluralResolver.getSuffixes(e,t);m&&t[`defaultValue${this.options.pluralSeparator}zero`]&&0>i.indexOf(`${this.options.pluralSeparator}zero`)&&i.push(`${this.options.pluralSeparator}zero`),i.forEach(i=>{r([e],o+i,t[`defaultValue${i}`]||v)})}):r(e,o,v))}h=this.extendTranslation(h,e,t,p,i),a&&h===o&&this.options.appendNamespaceToMissingKey&&(h=`${l}:${o}`),(a||s)&&this.options.parseMissingKeyHandler&&(h="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${o}`:o,s?h:void 0):this.options.parseMissingKeyHandler(h))}return s?(p.res=h,p.usedParams=this.getUsedParamsDetails(t),p):h}extendTranslation(e,t,i,s,n){var o=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...i},i.lng||this.language||s.usedLng,s.usedNS,s.usedKey,{resolved:s});else if(!i.skipInterpolation){let a;i.interpolation&&this.interpolator.init({...i,interpolation:{...this.options.interpolation,...i.interpolation}});let l=r(e)&&(i&&i.interpolation&&void 0!==i.interpolation.skipOnVariables?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);if(l){let t=e.match(this.interpolator.nestingRegexp);a=t&&t.length}let u=i.replace&&!r(i.replace)?i.replace:i;if(this.options.interpolation.defaultVariables&&(u={...this.options.interpolation.defaultVariables,...u}),e=this.interpolator.interpolate(e,u,i.lng||this.language||s.usedLng,i),l){let t=e.match(this.interpolator.nestingRegexp);a<(t&&t.length)&&(i.nest=!1)}!i.lng&&"v1"!==this.options.compatibilityAPI&&s&&s.res&&(i.lng=this.language||s.usedLng),!1!==i.nest&&(e=this.interpolator.nest(e,function(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return n&&n[0]===r[0]&&!i.context?(o.logger.warn(`It seems you are nesting recursively key: ${r[0]} in key: ${t[0]}`),null):o.translate(...r,t)},i)),i.interpolation&&this.interpolator.reset()}let a=i.postProcess||this.options.postProcess,l=r(a)?[a]:a;return null!=e&&l&&l.length&&!1!==i.applyPostProcessor&&(e=N.handle(l,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...s,usedParams:this.getUsedParamsDetails(i)},...i}:i,this)),e}resolve(e){let t,i,s,n,o,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return r(e)&&(e=[e]),e.forEach(e=>{if(this.isValidLookup(t))return;let l=this.extractFromKey(e,a),u=l.key;i=u;let d=l.namespaces;this.options.fallbackNS&&(d=d.concat(this.options.fallbackNS));let p=void 0!==a.count&&!r(a.count),h=p&&!a.ordinal&&0===a.count&&this.pluralResolver.shouldUseIntlApi(),c=void 0!==a.context&&(r(a.context)||"number"==typeof a.context)&&""!==a.context,g=a.lngs?a.lngs:this.languageUtils.toResolveHierarchy(a.lng||this.language,a.fallbackLng);d.forEach(e=>{this.isValidLookup(t)||(o=e,!Z[`${g[0]}-${e}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(o)&&(Z[`${g[0]}-${e}`]=!0,this.logger.warn(`key "${i}" for languages "${g.join(", ")}" won't get resolved as namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),g.forEach(i=>{let r;if(this.isValidLookup(t))return;n=i;let o=[u];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(o,u,i,e,a);else{let e;p&&(e=this.pluralResolver.getSuffix(i,a.count,a));let t=`${this.options.pluralSeparator}zero`,r=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(p&&(o.push(u+e),a.ordinal&&0===e.indexOf(r)&&o.push(u+e.replace(r,this.options.pluralSeparator)),h&&o.push(u+t)),c){let i=`${u}${this.options.contextSeparator}${a.context}`;o.push(i),p&&(o.push(i+e),a.ordinal&&0===e.indexOf(r)&&o.push(i+e.replace(r,this.options.pluralSeparator)),h&&o.push(i+t))}}for(;r=o.pop();)this.isValidLookup(t)||(s=r,t=this.getResource(i,e,r,a))}))})}),{res:t,usedKey:i,exactUsedKey:s,usedLng:n,usedNS:o}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,i,r):this.resourceStore.getResource(e,t,i,r)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.replace&&!r(e.replace),i=t?e.replace:e;if(t&&void 0!==e.count&&(i.count=e.count),this.options.interpolation.defaultVariables&&(i={...this.options.interpolation.defaultVariables,...i}),!t)for(let e of(i={...i},["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"]))delete i[e];return i}static hasDefaultValue(e){let t="defaultValue";for(let i in e)if(Object.prototype.hasOwnProperty.call(e,i)&&t===i.substring(0,t.length)&&void 0!==e[i])return!0;return!1}}let j=e=>e.charAt(0).toUpperCase()+e.slice(1);class A{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=O.create("languageUtils")}getScriptPartFromCode(e){if(!(e=L(e))||0>e.indexOf("-"))return null;let t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase())?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(!(e=L(e))||0>e.indexOf("-"))return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(r(e)&&e.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(e){}let t=["hans","hant","latn","cyrl","cans","mong","arab"],i=e.split("-");return this.options.lowerCaseLng?i=i.map(e=>e.toLowerCase()):2===i.length?(i[0]=i[0].toLowerCase(),i[1]=i[1].toUpperCase(),t.indexOf(i[1].toLowerCase())>-1&&(i[1]=j(i[1].toLowerCase()))):3===i.length&&(i[0]=i[0].toLowerCase(),2===i[1].length&&(i[1]=i[1].toUpperCase()),"sgn"!==i[0]&&2===i[2].length&&(i[2]=i[2].toUpperCase()),t.indexOf(i[1].toLowerCase())>-1&&(i[1]=j(i[1].toLowerCase())),t.indexOf(i[2].toLowerCase())>-1&&(i[2]=j(i[2].toLowerCase()))),i.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){let t;return e?(e.forEach(e=>{if(t)return;let i=this.formatLanguageCode(e);(!this.options.supportedLngs||this.isSupportedCode(i))&&(t=i)}),!t&&this.options.supportedLngs&&e.forEach(e=>{if(t)return;let i=this.getLanguagePartFromCode(e);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find(e=>{if(e===i||!(0>e.indexOf("-")&&0>i.indexOf("-"))&&(e.indexOf("-")>0&&0>i.indexOf("-")&&e.substring(0,e.indexOf("-"))===i||0===e.indexOf(i)&&i.length>1))return e})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t):null}getFallbackCodes(e,t){if(!e)return[];if("function"==typeof e&&(e=e(t)),r(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let i=e[t];return i||(i=e[this.getScriptPartFromCode(t)]),i||(i=e[this.formatLanguageCode(t)]),i||(i=e[this.getLanguagePartFromCode(t)]),i||(i=e.default),i||[]}toResolveHierarchy(e,t){let i=this.getFallbackCodes(t||this.options.fallbackLng||[],e),s=[],n=e=>{e&&(this.isSupportedCode(e)?s.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return r(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&n(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&n(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&n(this.getLanguagePartFromCode(e))):r(e)&&n(this.formatLanguageCode(e)),i.forEach(e=>{0>s.indexOf(e)&&n(this.formatLanguageCode(e))}),s}}let M=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],F={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)},E=["v1","v2","v3"],D=["v4"],T={zero:0,one:1,two:2,few:3,many:4,other:5},V=()=>{let e={};return M.forEach(t=>{t.lngs.forEach(i=>{e[i]={numbers:t.nr,plurals:F[t.fc]}})}),e};class z{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=O.create("pluralResolver"),(!this.options.compatibilityJSON||D.includes(this.options.compatibilityJSON))&&("undefined"==typeof Intl||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=V(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){let i;let r=L("dev"===e?"en":e),s=t.ordinal?"ordinal":"cardinal",n=JSON.stringify({cleanedCode:r,type:s});if(n in this.pluralRulesCache)return this.pluralRulesCache[n];try{i=new Intl.PluralRules(r,{type:s})}catch(s){if(!e.match(/-|_/))return;let r=this.languageUtils.getLanguagePartFromCode(e);i=this.getRule(r,t)}return this.pluralRulesCache[n]=i,i}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=this.getRule(e,t);return this.shouldUseIntlApi()?i&&i.resolvedOptions().pluralCategories.length>1:i&&i.numbers.length>1}getPluralFormsOfKey(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,i).map(e=>`${t}${e}`)}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=this.getRule(e,t);return i?this.shouldUseIntlApi()?i.resolvedOptions().pluralCategories.sort((e,t)=>T[e]-T[t]).map(e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`):i.numbers.map(i=>this.getSuffix(e,i,t)):[]}getSuffix(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=this.getRule(e,i);return r?this.shouldUseIntlApi()?`${this.options.prepend}${i.ordinal?`ordinal${this.options.prepend}`:""}${r.select(t)}`:this.getSuffixRetroCompatible(r,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){let i=e.noAbs?e.plurals(t):e.plurals(Math.abs(t)),r=e.numbers[i];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===r?r="plural":1===r&&(r=""));let s=()=>this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString();return"v1"===this.options.compatibilityJSON?1===r?"":"number"==typeof r?`_plural_${r.toString()}`:s():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?s():this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString()}shouldUseIntlApi(){return!E.includes(this.options.compatibilityJSON)}}let U=function(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",n=!(arguments.length>4)||void 0===arguments[4]||arguments[4],o=g(e,t,i);return!o&&n&&r(i)&&void 0===(o=w(e,i,s))&&(o=w(t,i,s)),o},B=e=>e.replace(/\$/g,"$$$$");class H{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=O.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let{escape:t,escapeValue:i,useRawValueToEscape:r,prefix:s,prefixEscaped:n,suffix:o,suffixEscaped:a,formatSeparator:l,unescapeSuffix:u,unescapePrefix:d,nestingPrefix:p,nestingPrefixEscaped:h,nestingSuffix:c,nestingSuffixEscaped:g,nestingOptionsSeparator:f,maxReplaces:v,alwaysFormat:b}=e.interpolation;this.escape=void 0!==t?t:y,this.escapeValue=void 0===i||i,this.useRawValueToEscape=void 0!==r&&r,this.prefix=s?m(s):n||"{{",this.suffix=o?m(o):a||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":d||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=p?m(p):h||m("$t("),this.nestingSuffix=c?m(c):g||m(")"),this.nestingOptionsSeparator=f||",",this.maxReplaces=v||1e3,this.alwaysFormat=void 0!==b&&b,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,i,s){let o,a,l;let u=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},d=e=>{if(0>e.indexOf(this.formatSeparator)){let r=U(t,u,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(r,void 0,i,{...s,...t,interpolationkey:e}):r}let r=e.split(this.formatSeparator),n=r.shift().trim(),o=r.join(this.formatSeparator).trim();return this.format(U(t,u,n,this.options.keySeparator,this.options.ignoreJSONStructure),o,i,{...s,...t,interpolationkey:n})};this.resetRegExp();let p=s&&s.missingInterpolationHandler||this.options.missingInterpolationHandler,h=s&&s.interpolation&&void 0!==s.interpolation.skipOnVariables?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>B(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?B(this.escape(e)):B(e)}].forEach(t=>{for(l=0;o=t.regex.exec(e);){let i=o[1].trim();if(void 0===(a=d(i))){if("function"==typeof p){let t=p(e,o,s);a=r(t)?t:""}else if(s&&Object.prototype.hasOwnProperty.call(s,i))a="";else if(h){a=o[0];continue}else this.logger.warn(`missed to pass in variable ${i} for interpolating ${e}`),a=""}else r(a)||this.useRawValueToEscape||(a=n(a));let u=t.safeValue(a);if(e=e.replace(o[0],u),h?(t.regex.lastIndex+=a.length,t.regex.lastIndex-=o[0].length):t.regex.lastIndex=0,++l>=this.maxReplaces)break}}),e}nest(e,t){let i,s,o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=(e,t)=>{let i=this.nestingOptionsSeparator;if(0>e.indexOf(i))return e;let r=e.split(RegExp(`${i}[ ]*{`)),s=`{${r[1]}`;e=r[0];let n=(s=this.interpolate(s,o)).match(/'/g),a=s.match(/"/g);(n&&n.length%2==0&&!a||a.length%2!=0)&&(s=s.replace(/'/g,'"'));try{o=JSON.parse(s),t&&(o={...t,...o})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${i}${s}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,e};for(;i=this.nestingRegexp.exec(e);){let u=[];(o=(o={...a}).replace&&!r(o.replace)?o.replace:o).applyPostProcessor=!1,delete o.defaultValue;let d=!1;if(-1!==i[0].indexOf(this.formatSeparator)&&!/{.*}/.test(i[1])){let e=i[1].split(this.formatSeparator).map(e=>e.trim());i[1]=e.shift(),u=e,d=!0}if((s=t(l.call(this,i[1].trim(),o),o))&&i[0]===e&&!r(s))return s;r(s)||(s=n(s)),s||(this.logger.warn(`missed to resolve ${i[1]} for nesting ${e}`),s=""),d&&(s=u.reduce((e,t)=>this.format(e,t,a.lng,{...a,interpolationkey:i[1].trim()}),s.trim())),e=e.replace(i[0],s),this.regexp.lastIndex=0}return e}}let W=e=>{let t=e.toLowerCase().trim(),i={};if(e.indexOf("(")>-1){let r=e.split("(");t=r[0].toLowerCase().trim();let s=r[1].substring(0,r[1].length-1);"currency"===t&&0>s.indexOf(":")?i.currency||(i.currency=s.trim()):"relativetime"===t&&0>s.indexOf(":")?i.range||(i.range=s.trim()):s.split(";").forEach(e=>{if(e){let[t,...r]=e.split(":"),s=r.join(":").trim().replace(/^'+|'+$/g,""),n=t.trim();i[n]||(i[n]=s),"false"===s&&(i[n]=!1),"true"===s&&(i[n]=!0),isNaN(s)||(i[n]=parseInt(s,10))}})}return{formatName:t,formatOptions:i}},K=e=>{let t={};return(i,r,s)=>{let n=s;s&&s.interpolationkey&&s.formatParams&&s.formatParams[s.interpolationkey]&&s[s.interpolationkey]&&(n={...n,[s.interpolationkey]:void 0});let o=r+JSON.stringify(n),a=t[o];return a||(a=e(L(r),s),t[o]=a),a(i)}};class J{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=O.create("formatter"),this.options=e,this.formats={number:K((e,t)=>{let i=new Intl.NumberFormat(e,{...t});return e=>i.format(e)}),currency:K((e,t)=>{let i=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>i.format(e)}),datetime:K((e,t)=>{let i=new Intl.DateTimeFormat(e,{...t});return e=>i.format(e)}),relativetime:K((e,t)=>{let i=new Intl.RelativeTimeFormat(e,{...t});return e=>i.format(e,t.range||"day")}),list:K((e,t)=>{let i=new Intl.ListFormat(e,{...t});return e=>i.format(e)})},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=K(t)}format(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=t.split(this.formatSeparator);if(s.length>1&&s[0].indexOf("(")>1&&0>s[0].indexOf(")")&&s.find(e=>e.indexOf(")")>-1)){let e=s.findIndex(e=>e.indexOf(")")>-1);s[0]=[s[0],...s.splice(1,e)].join(this.formatSeparator)}return s.reduce((e,t)=>{let{formatName:s,formatOptions:n}=W(t);if(this.formats[s]){let t=e;try{let o=r&&r.formatParams&&r.formatParams[r.interpolationkey]||{},a=o.locale||o.lng||r.locale||r.lng||i;t=this.formats[s](e,a,{...n,...r,...o})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${s}`),e},e)}}let G=(e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)};class q extends ${constructor(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=i,this.languageUtils=i.languageUtils,this.options=r,this.logger=O.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=r.maxParallelReads||10,this.readingCalls=0,this.maxRetries=r.maxRetries>=0?r.maxRetries:5,this.retryTimeout=r.retryTimeout>=1?r.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(i,r.backend,r)}queueLoad(e,t,i,r){let s={},n={},o={},a={};return e.forEach(e=>{let r=!0;t.forEach(t=>{let o=`${e}|${t}`;!i.reload&&this.store.hasResourceBundle(e,t)?this.state[o]=2:this.state[o]<0||(1===this.state[o]?void 0===n[o]&&(n[o]=!0):(this.state[o]=1,r=!1,void 0===n[o]&&(n[o]=!0),void 0===s[o]&&(s[o]=!0),void 0===a[t]&&(a[t]=!0)))}),r||(o[e]=!0)}),(Object.keys(s).length||Object.keys(n).length)&&this.queue.push({pending:n,pendingCount:Object.keys(n).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(s),pending:Object.keys(n),toLoadLanguages:Object.keys(o),toLoadNamespaces:Object.keys(a)}}loaded(e,t,i){let r=e.split("|"),s=r[0],n=r[1];t&&this.emit("failedLoading",s,n,t),!t&&i&&this.store.addResourceBundle(s,n,i,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&i&&(this.state[e]=0);let o={};this.queue.forEach(i=>{h(i.loaded,[s],n),G(i,e),t&&i.errors.push(t),0!==i.pendingCount||i.done||(Object.keys(i.loaded).forEach(e=>{o[e]||(o[e]={});let t=i.loaded[e];t.length&&t.forEach(t=>{void 0===o[e][t]&&(o[e][t]=!0)})}),i.done=!0,i.errors.length?i.callback(i.errors):i.callback())}),this.emit("loaded",o),this.queue=this.queue.filter(e=>!e.done)}read(e,t,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,n=arguments.length>5?arguments[5]:void 0;if(!e.length)return n(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:i,tried:r,wait:s,callback:n});return}this.readingCalls++;let o=(o,a)=>{if(this.readingCalls--,this.waitingReads.length>0){let e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}if(o&&a&&r<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,i,r+1,2*s,n)},s);return}n(o,a)},a=this.backend[i].bind(this.backend);if(2===a.length){try{let i=a(e,t);i&&"function"==typeof i.then?i.then(e=>o(null,e)).catch(o):o(null,i)}catch(e){o(e)}return}return a(e,t,o)}prepareLoading(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},s=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),s&&s();r(e)&&(e=this.languageUtils.toResolveHierarchy(e)),r(t)&&(t=[t]);let n=this.queueLoad(e,t,i,s);if(!n.toLoad.length)return n.pending.length||s(),null;n.toLoad.forEach(e=>{this.loadOne(e)})}load(e,t,i){this.prepareLoading(e,t,{},i)}reload(e,t,i){this.prepareLoading(e,t,{reload:!0},i)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=e.split("|"),r=i[0],s=i[1];this.read(r,s,"read",void 0,void 0,(i,n)=>{i&&this.logger.warn(`${t}loading namespace ${s} for language ${r} failed`,i),!i&&n&&this.logger.log(`${t}loaded namespace ${s} for language ${r}`,n),this.loaded(e,i,n)})}saveMissing(e,t,i,r,s){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},o=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${i}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(null!=i&&""!==i){if(this.backend&&this.backend.create){let a={...n,isUpdate:s},l=this.backend.create.bind(this.backend);if(l.length<6)try{let s;(s=5===l.length?l(e,t,i,r,a):l(e,t,i,r))&&"function"==typeof s.then?s.then(e=>o(null,e)).catch(o):o(null,s)}catch(e){o(e)}else l(e,t,i,r,o,a)}e&&e[0]&&this.store.addResource(e[0],t,i,r)}}}let _=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"==typeof e[1]&&(t=e[1]),r(e[1])&&(t.defaultValue=e[1]),r(e[2])&&(t.tDescription=e[2]),"object"==typeof e[2]||"object"==typeof e[3]){let i=e[3]||e[2];Object.keys(i).forEach(e=>{t[e]=i[e]})}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),Y=e=>(r(e.ns)&&(e.ns=[e.ns]),r(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),r(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&0>e.supportedLngs.indexOf("cimode")&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),Q=()=>{},X=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(t=>{"function"==typeof e[t]&&(e[t]=e[t].bind(e))})};class ee extends ${constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=Y(e),this.services={},this.logger=O,this.modules={external:[]},X(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof t&&(i=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(r(t.ns)?t.defaultNS=t.ns:0>t.ns.indexOf("translation")&&(t.defaultNS=t.ns[0]));let n=_();this.options={...n,...this.options,...Y(t)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...n.interpolation,...this.options.interpolation}),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);let o=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?O.init(o(this.modules.logger),this.options):O.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!=typeof Intl&&(t=J);let i=new A(this.options);this.store=new P(this.options.resources,this.options);let r=this.services;r.logger=O,r.resourceStore=this.store,r.languageUtils=i,r.pluralResolver=new z(i,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),t&&(!this.options.interpolation.format||this.options.interpolation.format===n.interpolation.format)&&(r.formatter=o(t),r.formatter.init(r,this.options),this.options.interpolation.format=r.formatter.format.bind(r.formatter)),r.interpolator=new H(this.options),r.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},r.backendConnector=new q(o(this.modules.backend),r.resourceStore,r,this.options),r.backendConnector.on("*",function(t){for(var i=arguments.length,r=Array(i>1?i-1:0),s=1;s<i;s++)r[s-1]=arguments[s];e.emit(t,...r)}),this.modules.languageDetector&&(r.languageDetector=o(this.modules.languageDetector),r.languageDetector.init&&r.languageDetector.init(r,this.options.detection,this.options)),this.modules.i18nFormat&&(r.i18nFormat=o(this.modules.i18nFormat),r.i18nFormat.init&&r.i18nFormat.init(this)),this.translator=new I(this.services,this.options),this.translator.on("*",function(t){for(var i=arguments.length,r=Array(i>1?i-1:0),s=1;s<i;s++)r[s-1]=arguments[s];e.emit(t,...r)}),this.modules.external.forEach(e=>{e.init&&e.init(this)})}if(this.format=this.options.interpolation.format,i||(i=Q),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(t=>{this[t]=function(){return e.store[t](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(t=>{this[t]=function(){return e.store[t](...arguments),e}});let a=s(),l=()=>{let e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(t),i(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?l():setTimeout(l,0),a}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Q,i=t,s=r(e)?e:this.language;if("function"==typeof e&&(i=e),!this.options.resources||this.options.partialBundledLanguages){if(s&&"cimode"===s.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return i();let e=[],t=t=>{t&&"cimode"!==t&&this.services.languageUtils.toResolveHierarchy(t).forEach(t=>{"cimode"!==t&&0>e.indexOf(t)&&e.push(t)})};s?t(s):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(e=>t(e)),this.options.preload&&this.options.preload.forEach(e=>t(e)),this.services.backendConnector.load(e,this.options.ns,e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),i(e)})}else i(null)}reloadResources(e,t,i){let r=s();return"function"==typeof e&&(i=e,e=void 0),"function"==typeof t&&(i=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),i||(i=Q),this.services.backendConnector.reload(e,t,e=>{r.resolve(),i(e)}),r}use(e){if(!e)throw Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&N.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let e=0;e<this.languages.length;e++){let t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}}changeLanguage(e,t){var i=this;this.isLanguageChangingTo=e;let n=s();this.emit("languageChanging",e);let o=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},a=(e,r)=>{r?(o(r),this.translator.changeLanguage(r),this.isLanguageChangingTo=void 0,this.emit("languageChanged",r),this.logger.log("languageChanged",r)):this.isLanguageChangingTo=void 0,n.resolve(function(){return i.t(...arguments)}),t&&t(e,function(){return i.t(...arguments)})},l=t=>{e||t||!this.services.languageDetector||(t=[]);let i=r(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);i&&(this.language||o(i),this.translator.language||this.translator.changeLanguage(i),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(i)),this.loadResources(i,e=>{a(e,i)})};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e):l(this.services.languageDetector.detect()),n}getFixedT(e,t,i){var s=this;let n=function(e,t){let r,o;if("object"!=typeof t){for(var a=arguments.length,l=Array(a>2?a-2:0),u=2;u<a;u++)l[u-2]=arguments[u];r=s.options.overloadTranslationOptionHandler([e,t].concat(l))}else r={...t};r.lng=r.lng||n.lng,r.lngs=r.lngs||n.lngs,r.ns=r.ns||n.ns,""!==r.keyPrefix&&(r.keyPrefix=r.keyPrefix||i||n.keyPrefix);let d=s.options.keySeparator||".";return o=r.keyPrefix&&Array.isArray(e)?e.map(e=>`${r.keyPrefix}${d}${e}`):r.keyPrefix?`${r.keyPrefix}${d}${e}`:e,s.t(o,r)};return r(e)?n.lng=e:n.lngs=e,n.ns=t,n.keyPrefix=i,n}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let i=t.lng||this.resolvedLanguage||this.languages[0],r=!!this.options&&this.options.fallbackLng,s=this.languages[this.languages.length-1];if("cimode"===i.toLowerCase())return!0;let n=(e,t)=>{let i=this.services.backendConnector.state[`${e}|${t}`];return -1===i||0===i||2===i};if(t.precheck){let e=t.precheck(this,n);if(void 0!==e)return e}return!!(this.hasResourceBundle(i,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||n(i,e)&&(!r||n(s,e)))}loadNamespaces(e,t){let i=s();return this.options.ns?(r(e)&&(e=[e]),e.forEach(e=>{0>this.options.ns.indexOf(e)&&this.options.ns.push(e)}),this.loadResources(e=>{i.resolve(),t&&t(e)}),i):(t&&t(),Promise.resolve())}loadLanguages(e,t){let i=s();r(e)&&(e=[e]);let n=this.options.preload||[],o=e.filter(e=>0>n.indexOf(e)&&this.services.languageUtils.isSupportedCode(e));return o.length?(this.options.preload=n.concat(o),this.loadResources(e=>{i.resolve(),t&&t(e)}),i):(t&&t(),Promise.resolve())}dir(e){return(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),e)?["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf((this.services&&this.services.languageUtils||new A(_())).getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr":"rtl"}static createInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new ee(e,t)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Q,i=e.forkResourceStore;i&&delete e.forkResourceStore;let r={...this.options,...e,isClone:!0},s=new ee(r);return(void 0!==e.debug||void 0!==e.prefix)&&(s.logger=s.logger.clone(e)),["store","services","language"].forEach(e=>{s[e]=this[e]}),s.services={...this.services},s.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},i&&(s.store=new P(this.store.data,r),s.services.resourceStore=s.store),s.translator=new I(s.services,r),s.translator.on("*",function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),r=1;r<t;r++)i[r-1]=arguments[r];s.emit(e,...i)}),s.init(r,t),s.translator.options=r,s.translator.backendConnector.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},s}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}let et=ee.createInstance();et.createInstance=ee.createInstance;let ei=et.createInstance;et.dir,et.init,et.loadResources,et.reloadResources,et.use,et.changeLanguage,et.getFixedT,et.t,et.exists,et.setDefaultNamespace,et.hasLoadedNamespace,et.loadNamespaces,et.loadLanguages}}]);