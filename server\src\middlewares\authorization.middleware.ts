import HttpException from '@/utils/exceptions/http.exception';
import { Role } from '@/utils/helpers/constants';
import { MESSAGES } from '@/utils/helpers/messages';
import { Response, NextFunction } from 'express';

export const hasRoles = (roles: Role[]) => {
    return async (request: any, response: Response, next: NextFunction) => {
        const userRoles = request.user.roles;
        const hasRole = roles.some(role => userRoles.includes(role));

        if (!hasRole) {
            return next(new HttpException(403, MESSAGES.AUTH.FORBIDDEN));
        }
        return next();
    };
};
