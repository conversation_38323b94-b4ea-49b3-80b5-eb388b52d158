import { Container, Grid } from "@mui/material";
import EmblaCarouselThumbsTeamPic from "../ui/emblaCarousel/EmblaCarouselThumbsTeamPic";
import img1 from "../../assets/images/Tunisia/tn4.jpeg";
import img2 from "../../assets/images/Tunisia/tn3.jpeg";
import img3 from "../../assets/images/Tunisia/tn1.jpeg";
import img4 from "../../assets/images/Tunisia/tnh7.JPG";
function EORServiceSEgypt({ t }) {
  const OPTIONS = {};
  const SLIDE_COUNT = 10;
  const SLIDES = Array.from(Array(SLIDE_COUNT).keys());
  const slideImg = [{ src: img1 }, { src: img2 }, { src: img3 }, { src: img4 }];
  return (
    <Container id="eor-services-tn" className="custom-max-width">
      <Grid
        className="container"
        justifyContent="space-between"
        container
        spacing={2}
      >
        <Grid item xs={12} sm={6}>
          <div className="team-thumbs">
            <EmblaCarouselThumbsTeamPic slides={slideImg} options={OPTIONS} />
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <div className="right-section">
            <p className="paragraph text-yellow">
            {t("Egypte:EORServicesTN:subTitle")}
            </p>
            <p className="heading-h2 text-white">
            {t("Egypte:EORServicesTN:title")}
            </p>
            <p className="paragraph text-white">
            {t("Egypte:EORServicesTN:description")}
            </p>
          </div>
        </Grid>
      </Grid>
    </Container>
  );
}

export default EORServiceSEgypt;
