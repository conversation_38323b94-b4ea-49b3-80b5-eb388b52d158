import multer from 'multer';
import * as path from 'path';
import * as fs from 'fs';
import { Request, Response, NextFunction } from 'express';

import HttpException from '@/utils/exceptions/http.exception';

const imageMaxSize = 1024 * 1024 * 2;
const pdfmaxSize = 1024 * 1024 * 5;

const PDF_MIME_TYPES = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
const IMAGE_MIME_TYPES = ['image/png', 'image/jpg', 'image/jpeg','image/webp'];

const fileFilter = (request: any, file: any, callback: any) => {
    let allowedMimeTypes: any = [];
    let maxSize = 0;
    if (PDF_MIME_TYPES.includes(file.mimetype)) {
        allowedMimeTypes = PDF_MIME_TYPES;
        maxSize = pdfmaxSize;
    } else if (IMAGE_MIME_TYPES.includes(file.mimetype)) {
        allowedMimeTypes = IMAGE_MIME_TYPES;
        maxSize = imageMaxSize;
    }
    const isAllowed = allowedMimeTypes.length > 0 && allowedMimeTypes.includes(file.mimetype);

    if (isAllowed) {
        callback(null, true);
    } else {
        callback(new HttpException(400, 'Invalid file format'));
    }
};

const storage: any = multer.diskStorage({
    destination: function (request: any, file: any, callback: any) {
        const { resource, folder } = request.params;
        const uploadPath = path.join(__dirname, '../../uploads/', resource, folder);
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }
        callback(null, uploadPath);
    },
    filename: function (request: Request, file: any, callback: any) {
        const uuid = request.params.filename;
        const fileName = file.originalname.split('.');
        const fileExtension = file.originalname.split('.')[fileName.length - 1].toLowerCase();
        const newFilename = `${uuid}.${fileExtension}`;
        callback(null, newFilename);
    },
} as any);

const upload: any = multer({
    storage,
    fileFilter,
});

export const uploadMiddleware = (request: Request, response: Response, next: NextFunction) => {
    upload.single('file')(request, response, function (error: any) {
        if (error) {
            return next(new HttpException(400, `File upload failed ${error.message}`));
        }
        next();
    });
};
