"use client";
import { useEffect, useState } from "react";
import jwt from "jsonwebtoken";
import { useTranslation } from "react-i18next";
import SvgFailed from "@/assets/images/icons/Failed.svg";

import AuthLayout from "@/components/layouts/AuthLayout";
import ResetPwdForm from "@/features/auth/component/ResetPwdForm";
import CustomButton from "@/components/ui/CustomButton";
import { authRoutes } from "../../../../helpers/routesList";

const ResetPasswordPage = ({ searchParams, params: { locale } }) => {
  const { t } = useTranslation();
  const token = searchParams.token;
  const [isValidToken, setIsValidToken] = useState(null);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    const validateToken = () => {
      try {
        if (!token) {
          throw new Error("Missing Token");
        }

        const decodedToken = jwt.decode(token);

        if (!decodedToken) {
          throw new Error("Invalid token");
        }

        const currentTime = Math.floor(Date.now() / 1000);
        if (decodedToken.exp && decodedToken.exp < currentTime) {
          throw new Error("Token expired");
        }

        setIsValidToken(true);
      } catch (error) {
        setIsValidToken(false);
        setErrorMessage(error.message);
      }
    };

    validateToken();
  }, [token, t]);

  if (isValidToken === null) {
    return <div>Loading...</div>;
  }

  if (!isValidToken) {
    return (
      <AuthLayout id="auth-layout">
        <div class="text-center">
          <SvgFailed />
          <p className="heading-h1 text-white">{t("resetPassword:title")}</p>
          <p className="sub-heading text-white">
            {t("resetPassword:description")}
          </p>
          <CustomButton
            text={t("resetPassword:tryAgain")}
            className={"btn btn-filled full-width btn-submit"}
            link={`/${authRoutes.forgetPassword.route}`}
            locale={locale}
          />
        </div>
      </AuthLayout>
    );
  }

  return (
    <AuthLayout id="auth-layout" title={t("resetPassword:title")}>
      <ResetPwdForm token={token} t={t} locale={locale} />
    </AuthLayout>
  );
};

export default ResetPasswordPage;
