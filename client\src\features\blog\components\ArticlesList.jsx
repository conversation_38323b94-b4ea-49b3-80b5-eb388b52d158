"use client";
import { Container, Grid, InputAdornment, TextField } from "@mui/material";
import { useState, useEffect } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { useTranslation } from "react-i18next";

import CustomButton from "@/components/ui/CustomButton";
import { API_URLS } from "@/utils/urls";
import { axiosGetJsonSSR } from "@/config/axios";
import CustomPagination from "@/components/CustomPagination";
import SvgSearchIcon from "../../../assets/images/icons/searchIcon.svg";
import SvgRefreshIcon from "../../../assets/images/icons/refreshIcon.svg";
import BlogItem from "./BlogItem";
import GuideItem from "../../guides/components/GuideItem";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
// import SvgCategoriesIcon from "../../../assets/images/icons/categoriesIcon.svg";
import Autoplay from "embla-carousel-autoplay";
import { websiteRoutesList } from "@/helpers/routesList";
import { htmlToText } from "html-to-text";
import Link from "next/link";

export default function ArticlesList({
  data,
  language,
  searchParams,
  isCategory,
}) {
  const [pageNumber, setPageNumber] = useState(
    parseInt(searchParams?.pageNumber || 1)
  );
  const [keyword, setkeyword] = useState(searchParams?.keyword);

  const searchQueryParams = new URLSearchParams();

  const searchParamsVar = useSearchParams();
  const searchParamsContent = searchParamsVar.toString();

  const [slides, setSlides] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const { t } = useTranslation();

  const gradients = [
    "linear-gradient(to bottom, rgba(214, 155, 25, 0), rgba(214, 155, 25, 0.6))",
    "linear-gradient(to bottom, rgba(116, 55, 148, 0), rgba(116, 55, 148, 0.6))",
    "linear-gradient(to bottom, rgba(0, 153, 102, 0), rgba(0, 153, 102, 0.6))",
    "linear-gradient(to bottom, rgba(204, 50, 51, 0), rgba(204, 50, 51, 0.6))",
  ];

  const router = useRouter();
  const pathname = usePathname();

  const truncateDescription = (title) => {
    title = htmlToText(title, { wordwrap: false });
    const words = title.split(" ");
    if (words?.length >= 20) {
      return words.slice(0, 20).join(" ");
    } else {
      return title;
    }
  };

  const updateUrlWithParams = () => {
    if (keyword) searchQueryParams.set("keyword", keyword);
    if (pageNumber) searchQueryParams.set("pageNumber", 1);

    router.push(`${pathname}?${searchQueryParams.toString()}`);
  };

  const resetSearch = () => {
    setkeyword("");
    setPageNumber(1);
    searchQueryParams.set("pageNumber", 1);
    router.push(`${pathname}?${searchQueryParams.toString()}`);
  };

  const handlePageChange = (page) => {
    setPageNumber(page);
  };
  let lastColor = "";
  const getRandomGradient = () => {
    // return gradients[Math.floor(Math.random() * gradients.length)];
    let randomColor;
    do {
      randomColor = gradients[Math.floor(Math.random() * gradients.length)];
    } while (randomColor === lastColor);
    lastColor = randomColor;
    return randomColor;
  };

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const response = await axiosGetJsonSSR.get(
        `${language}${API_URLS.category}`
      );

      const categoriesData = response.data.categoriesData.map((category) => {
        const firstVersion = category?.versionscategory[0];
        return {
          img: firstVersion?.image
            ? `${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${firstVersion?.image}`
            : null,

          link: `${firstVersion?.url}`,
          name: firstVersion?.name || "N/A",
        };
      });

      setSlides(categoriesData);
    } catch (error) {
      setError(t("messages:fetchCategoriesFailed"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  const OPTIONS = { loop: true, align: "start" };
  const [emblaRef] = useEmblaCarousel(OPTIONS, [
    Autoplay({ playOnInit: true, delay: 1000, stopOnMouseEnter: true }),
  ]);
  const OPTIONS_Right = { loop: true, align: "start", direction: "rtl" };
  const [emblaRefRight] = useEmblaCarousel(OPTIONS_Right, [
    Autoplay({ playOnInit: true, delay: 1000, stopOnMouseEnter: true }),
  ]);

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      updateUrlWithParams();
    }
  };

  return (
    <div id="blogs-page">
      {!isCategory && (
        <div id="search-bar-blogs">
          <Container className="custom-max-width">
            <Grid className="container" container spacing={0}>
              <Grid
                item
                xs={12}
                sm={8}
                container
                spacing={0}
                className="filter-inputs"
              >
                <Grid item xs={12} sm={12}>
                  <TextField
                    className="input-pentabell"
                    autoComplete="off"
                    slotProps={{
                      input: {
                        startAdornment: (
                          <InputAdornment position="start">
                            <SvgSearchIcon />
                          </InputAdornment>
                        ),
                      },
                    }}
                    variant="standard"
                    type="text"
                    value={keyword}
                    onChange={(e) => setkeyword(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={"Keywords"}
                  />
                </Grid>
              </Grid>
              <Grid item xs={12} sm={4} className="btns-filter">
                <CustomButton
                  icon={<SvgRefreshIcon />}
                  className={"btn btn-outlined"}
                  onClick={resetSearch}
                />
                <CustomButton
                  text={"Search"}
                  onClick={updateUrlWithParams}
                  className={"btn btn-filled full-width"}
                />
              </Grid>
            </Grid>
          </Container>
        </div>
      )}
      <Container className="custom-max-width">
        <p className="heading-h1">Browse by topic</p>
        <section className="embla" id={"blog__categories__slider"}>
          <div className="embla__viewport" ref={emblaRef}>
            <div className="embla__container categories-list">
              {slides.map(
                (category, index) =>
                  category?.link && (
                    <span className="category-2" key={category.link}>
                      <a
                        href={`${
                          language === "en"
                            ? `/${websiteRoutesList.blog.route}/${websiteRoutesList.category.route}/${category.link}`
                            : `/${language}/${websiteRoutesList.blog.route}/${websiteRoutesList.category.route}/${category.link}`
                        }/`}
                      >
                        {category.name}
                      </a>
                    </span>
                  )
              )}
            </div>
          </div>
        </section>
        <section
          className="embla"
          style={{ marginBottom: "20px" }}
          id={"blog__categories__slider"}
          dir="rtl"
        >
          <div className="embla__viewport" ref={emblaRefRight}>
            <div className="embla__container categories-list">
              {slides.map(
                (category, index) =>
                  category?.link && (
                    <span className="category-2" key={category.link}>
                      <a
                        href={`${
                          language === "en"
                            ? `/${websiteRoutesList.blog.route}/${websiteRoutesList.category.route}/${category.link}`
                            : `/${language}/${websiteRoutesList.blog.route}/${websiteRoutesList.category.route}/${category.link}`
                        }/`}
                      >
                        {category.name}
                      </a>
                    </span>
                  )
              )}
            </div>
          </div>
        </section>
      </Container>
      <Container className="custom-max-width">
        {data?.firstArticle &&
          (() => {
            const { getBlogVersion } = require("@/utils/blogHelpers");
            const featuredVersionData = getBlogVersion(
              data.firstArticle,
              language
            );

            if (!featuredVersionData) {
              return null;
            }

            return (
              <>
                {/* <p className="heading-h1">Featured and trending</p> */}
                <div className="first-blog">
                  <div className="last-blog">
                    <Link
                      style={{ textDecoration: "none" }}
                      locale={language === "en" ? "en" : "fr"}
                      href={`${
                        language === "en"
                          ? `/${websiteRoutesList.blog.route}/${featuredVersionData?.url}`
                          : `/${language}/${websiteRoutesList.blog.route}/${featuredVersionData?.url}`
                      }/`}
                    >
                      <p className="title featured-title">
                        {featuredVersionData?.title}
                      </p>
                    </Link>
                    <p className="description">
                      {featuredVersionData?.description
                        ? featuredVersionData?.description
                        : truncateDescription(featuredVersionData?.content)}
                    </p>
                    <CustomButton
                      text={t("global:readMore")}
                      className={"btn btn-outlined"}
                      link={`/${websiteRoutesList.blog.route}/${featuredVersionData?.url}`}
                      aHref
                    />
                  </div>
                  <Link
                    style={{ textDecoration: "none" }}
                    locale={language === "en" ? "en" : "fr"}
                    href={`${
                      language === "en"
                        ? `/${websiteRoutesList.blog.route}/${featuredVersionData?.url}`
                        : `/${language}/${websiteRoutesList.blog.route}/${featuredVersionData?.url}`
                    }/`}
                  >
                    <div className="blog-img-section">
                      <div
                        className="img-section"
                        style={{
                          backgroundImage: `url(${process.env.NEXT_PUBLIC_BASE_API_URL}/files/${featuredVersionData?.image})`,
                        }}
                      />
                    </div>
                  </Link>
                </div>
              </>
            );
          })()}
        <Grid container rowSpacing={0} columnSpacing={2}>
          {data?.items?.map((item, index) => {
            if (item.type === "guide") {
              return (
                <GuideItem
                  key={`guide-${index}`}
                  guideData={item}
                  language={language}
                />
              );
            }

            if (item.type === "article") {
              return (
                <BlogItem
                  key={`article-${index}`}
                  blogData={item}
                  language={language}
                />
              );
            }

            return null;
          })}
        </Grid>

        {data?.totalItems > 0 && (
          <CustomPagination
            type="ssr"
            totalPages={Math.ceil(data?.totalItems / data?.pageSize)}
            currentPage={pageNumber}
            onPageChange={handlePageChange}
            searchQueryParams={searchParamsContent}
          />
        )}
      </Container>
    </div>
  );
}
