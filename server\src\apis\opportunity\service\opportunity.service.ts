import axios from 'axios';
import path from 'path';
import fs from 'fs';
import { Types } from 'mongoose';
import HttpException from '@/utils/exceptions/http.exception';
import { OpportunityI, OpportunityVersion } from '../opportunity.interface';
import OpportunityModel from '../model/opportunity.model';
import { Language, languages, OpportunityType, Visibility } from '@/utils/helpers/constants';
import { OpportunityQuery } from 'types/request/OpportunityQuery';
import { MESSAGES } from '@/utils/helpers/messages';
import { delay, generateJobDescription, translateJobTitle } from '@/utils/helpers/functions';

class OpportunityService {
    private readonly Opportunity = OpportunityModel;

    public async deleteOpportunityByLanguageAndId(language: Language, opportunityId: string): Promise<void> {
        const foundOpportunity: OpportunityI | null = await this.Opportunity.findById(opportunityId);
        if (!foundOpportunity) throw new HttpException(404, MESSAGES.OPPORTUNITY.NOT_FOUND);

        delete foundOpportunity.versions[language];
        await foundOpportunity.save();

        if (Object.keys(foundOpportunity.versions).length === 0) await this.Opportunity.findByIdAndDelete(opportunityId);
    }

    public async getOpportunityByUrl(language: string, url: string) {
        const opportunity = await this.Opportunity.findOne(
            { [`versions.${language}.url`]: url },
            {
                versions: 1,
                industry: 1,
                country: 1,
                contractType: 1,
                reference: 1,
                dateOfExpiration: 1,
                dateOfRequisition: 1,
            },
        );
        if (!opportunity) throw new HttpException(404, MESSAGES.OPPORTUNITY.NOT_FOUND);

        return opportunity;
    }

    public async get(opportunityId: string) {
        const opportunity = await this.Opportunity.findById(opportunityId).lean();
        if (!opportunity) throw new HttpException(404, MESSAGES.OPPORTUNITY.NOT_FOUND);
        return opportunity;
    }

    private readonly experienceMapping: Record<string, { min: number; max: number }> = {
        'Entry Level': { min: 0, max: 2 },
        Intermediate: { min: 2, max: 10 },
        Expert: { min: 10, max: Infinity },
    };

    public async getAll(queries: OpportunityQuery): Promise<
        | {
            pageNumber: number;
            pageSize: number;
            totalPages: number;
            totalOpportunities: number;
            opportunities: Array<OpportunityI & { existingLanguages: string[] }>;
        }
        | Array<OpportunityI & { existingLanguages: string[] }>
    > {
        const {
            paginated = 'true',
            keyWord,
            language = 'en',
            sortOrder,
            industry,
            contractType,
            genre,
            createdAt,
            country,
            isPublished,
            pageNumber = 1,
            pageSize = 6,
            visibility,
            minExperience,
            maxExperience,
            jobDescriptionLanguages,
            reference,
            opportunityType,
            exclude,
            levelOfExperience,
        } = queries;

        const queryConditions: Record<string, any> = this.buildQueryConditions({
            language,
            exclude,
            createdAt,
            reference,
            opportunityType,
            genre,
            industry,
            contractType,
            country,
            isPublished,
            visibility,
            levelOfExperience,
            minExperience,
            maxExperience,
            jobDescriptionLanguages,
        });

        let opportunitySearchRequest = queryConditions;
        let opportunityMetadata: Record<string, any> = {};

        if (keyWord) {
            if (keyWord.toUpperCase().includes('H-') || keyWord.toUpperCase().includes('C-')) {
                queryConditions['reference'] = RegExp(`.*${keyWord}.*`, 'i');
            } else {
                opportunitySearchRequest = { $text: { $search: keyWord }, ...queryConditions };
                opportunityMetadata = { score: { $meta: 'textScore' } };
            }
        }

        const sortCriteria: Record<string, any> = {
            ...opportunityMetadata,
            [`versions.${language}.createdAt`]: sortOrder === 'asc' ? 1 : -1,
        };

        const skip = (Number(pageNumber) - 1) * Number(pageSize);
        const limit = Number(pageSize);

        const opportunities = await this.Opportunity.find(opportunitySearchRequest, opportunityMetadata)
            .sort(sortCriteria)
            .skip(skip)
            .limit(limit)
            .select(
                'title industry country status reference opportunityType contractType expirationDate minExperience maxExperience dateOfExpiration createdAt versions',
            )
            .lean();

        const filteredOpportunities = this.processOpportunities(opportunities);

        if (paginated === 'false') {
            return filteredOpportunities;
        }

        const totalOpportunities = await this.Opportunity.countDocuments(opportunitySearchRequest);
        const totalPages = Math.ceil(totalOpportunities / limit);

        return {
            pageNumber: Number(pageNumber),
            pageSize: limit,
            totalPages,
            totalOpportunities,
            opportunities: filteredOpportunities,
        };
    }

    private buildQueryConditions({
        language,
        exclude,
        createdAt,
        reference,
        opportunityType,
        genre,
        industry,
        contractType,
        country,
        isPublished,
        visibility,
        levelOfExperience,
        minExperience,
        maxExperience,
        jobDescriptionLanguages,
    }: {
        language: string;
        exclude?: string;
        createdAt?: number;
        reference?: string;
        opportunityType?: OpportunityType;
        genre?: string;
        industry?: string;
        contractType?: string;
        country?: string;
        isPublished?: boolean;
        visibility?: string;
        levelOfExperience?: string;
        minExperience?: number | string;
        maxExperience?: number | string;
        jobDescriptionLanguages?: string;
    }): Record<string, any> {
        const queryConditions: Record<string, any> = {};

        if (exclude === 'true') {
            queryConditions['contractType'] = { $nin: ['Internship'] };
            queryConditions['opportunityType'] = { $nin: [OpportunityType.IN_HOUSE] };
        }

        if (createdAt) {
            const date = new Date(createdAt);
            queryConditions['createdAt'] = {
                $gte: new Date(date.getFullYear(), date.getMonth(), date.getDate()),
                $lte: new Date(date.getFullYear(), date.getMonth(), date.getDate(), 23, 59, 59, 999),
            };
        }

        if (reference) {
            queryConditions['reference'] = new RegExp(reference, 'i');
        }

        if (opportunityType) {
            queryConditions['opportunityType'] = opportunityType;
        }

        if (genre) {
            queryConditions[`versions.${language}.gender`] = genre;
        }

        if (industry) {
            queryConditions['industry'] = {
                $in: industry.split(',').map(i => i.trim()),
            };
        }

        if (contractType) {
            queryConditions['contractType'] = {
                $in: contractType.split(',').map(ct => ct.trim()),
            };
        }

        if (country) {
            queryConditions['country'] = new RegExp(country, 'i');
        }

        if (isPublished !== undefined) {
            queryConditions['isPublished'] = isPublished;
        }

        if (visibility) {
            queryConditions[`versions.${language}.visibility`] = visibility;
        }

        this.addExperienceFilters(queryConditions, levelOfExperience, minExperience, maxExperience);

        if (jobDescriptionLanguages) {
            const regex = jobDescriptionLanguages.split(',').map(lang => new RegExp(lang, 'i'));
            queryConditions[`versions.${language}.jobDescription`] = { $in: regex };
        }

        return queryConditions;
    }

    private addExperienceFilters(
        queryConditions: Record<string, any>,
        levelOfExperience?: string,
        minExperience?: number | string,
        maxExperience?: number | string,
    ): void {
        if (!levelOfExperience && !minExperience && !maxExperience) {
            return;
        }

        queryConditions.$and = queryConditions.$and ?? [];

        this.addPredefinedExperienceLevel(queryConditions, levelOfExperience);
        this.addExplicitExperienceValues(queryConditions, minExperience, maxExperience);
    }

    private addPredefinedExperienceLevel(queryConditions: Record<string, any>, levelOfExperience?: string): void {
        if (!levelOfExperience || !this.experienceMapping[levelOfExperience]) {
            return;
        }

        const { min, max } = this.experienceMapping[levelOfExperience];

        if (min !== undefined) {
            queryConditions.$and.push({ minExperience: { $gte: min } });
        }

        if (max !== Infinity) {
            queryConditions.$and.push({ maxExperience: { $lte: max } });
        }
    }

    private addExplicitExperienceValues(
        queryConditions: Record<string, any>,
        minExperience?: number | string,
        maxExperience?: number | string,
    ): void {
        if (!minExperience && !maxExperience) {
            return;
        }

        const mappedMin = this.mapExperienceValue('min', minExperience);
        const mappedMax = this.mapExperienceValue('max', maxExperience);

        if (mappedMin !== undefined) {
            queryConditions.$and.push({ minExperience: { $gte: mappedMin } });
        }

        if (mappedMax !== undefined && mappedMax !== Infinity) {
            queryConditions.$and.push({ maxExperience: { $lte: mappedMax } });
        }
    }

    private mapExperienceValue(type: 'min' | 'max', value?: number | string): number | undefined {
        if (typeof value === 'string' && this.experienceMapping[value]) {
            return this.experienceMapping[value][type];
        } else if (typeof value === 'number') {
            return value;
        }
        return undefined;
    }

    private processOpportunities(opportunities: any[]): Array<OpportunityI & { existingLanguages: string[] }> {
        return opportunities
            .map(opportunity => {
                const versions = opportunity?.versions ?? {};
                const existingLanguages = Object.keys(versions);
                return {
                    ...opportunity,
                    existingLanguages,
                };
            })
            .filter(opportunity => {
                const versions = opportunity?.versions ?? {};
                return Object.keys(versions).length > 0;
            });
    }

    public async getDistinctCountries(): Promise<string[]> {
        try {
            return (await OpportunityModel.distinct('country')) as string[];
        } catch (error) {
            throw new HttpException(500, MESSAGES.GENERAL.SERVER_ERROR);
        }
    }

    public async toggleArchivedOppportunity(language: Language, opportunityId: string, archive: boolean): Promise<void> {
        const opportunity = await this.get(opportunityId);

        opportunity.versions[language].isArchived = archive;
        await opportunity.save();
    }

    public async archiveOpportunity(opportunityId: string): Promise<void> {
        const opportunity = await this.get(opportunityId);

        languages.forEach(lang => {
            opportunity.versions[lang].isArchived = true;
            opportunity.versions[lang].visibility = Visibility.Private;
        });

        await opportunity.save();
    }

    public async getAllUrgentLatest(queries: OpportunityQuery): Promise<Array<OpportunityI & { existingLanguages: string[] }>> {
        const {
            keyWord,
            industry,
            contractType,
            genre,
            createdAt,
            country,
            urgent,
            isPublished,
            visibility,
            minExperience,
            maxExperience,
            jobDescriptionLanguages,
            language = Language.ENGLISH,
            levelOfExperience,
            reference,
            opportunityType,
            exclude,
        } = queries;

        const queryConditions: Record<string, any> = this.buildQueryConditions({
            language,
            exclude,
            createdAt,
            reference,
            opportunityType,
            genre,
            industry,
            contractType,
            country,
            isPublished,
            visibility,
            levelOfExperience,
            minExperience,
            maxExperience,
            jobDescriptionLanguages,
        });

        queryConditions['dateOfExpiration'] = { $gt: new Date() };
        if (urgent !== undefined) {
            queryConditions['urgent'] = true;
        }
        if (language) {
            queryConditions[`versions.${language}.language`] = language;
        }

        const opportunitySearchRequest = keyWord ? { $text: { $search: keyWord }, ...queryConditions } : queryConditions;
        const opportunityMetadata: Record<string, any> = keyWord ? { score: { $meta: 'textScore' } } : {};

        const sortCriteria: Record<string, any> = {
            ...opportunityMetadata,
            createdAt: -1,
        };

        const opportunities = await this.Opportunity.find(opportunitySearchRequest, opportunityMetadata)
            .sort(sortCriteria)
            .limit(4)
            .select(
                'title industry country status contractType reference minExperience maxExperience dateOfExpiration urgent latest createdAt versions',
            )
            .lean();

        return this.processOpportunities(opportunities);
    }

    public async updateMainOpportunityFields(opportunityId: string, updateData: any) {
        await this.get(opportunityId);

        return await this.Opportunity.findByIdAndUpdate(opportunityId, { $set: updateData }, { new: true });
    }

    public async delete(opportunityId: string): Promise<void> {
        await this.get(opportunityId);
        await this.Opportunity.findByIdAndDelete(opportunityId);
    }

    private async updateOpportuniteBylanguageandId(
        language: Language,
        opportunityId: string,
        updateData: Partial<OpportunityVersion>,
    ): Promise<OpportunityI | null> {
        await this.get(opportunityId);

        return await this.Opportunity.findByIdAndUpdate(
            opportunityId,
            { [`versions.${language}`]: { ...updateData, updatedAt: new Date() } },
            { new: true },
        );
    }

    private async addVersionToOpportunity(opportunityId: string, newVersion: OpportunityVersion) {
        const existingOpportunity = await this.Opportunity.findById(opportunityId);
        if (!existingOpportunity) throw new HttpException(404, MESSAGES.OPPORTUNITY.NOT_FOUND);

        newVersion.url = this.createJobUrl(newVersion.title, existingOpportunity.reference);
        newVersion.alt = newVersion.alt ?? newVersion.title;

        existingOpportunity.versions[newVersion.language] = newVersion;
        const updatedOpportunity = await existingOpportunity.save();

        return updatedOpportunity;
    }

    public async getSlugBySlug(language: string, url: string): Promise<{ slug: string }> {
        const targetLanguage = language === 'en' ? 'fr' : 'en';

        const opportunity = await this.Opportunity.findOne({
            [`versions.${language}.language`]: language,
            [`versions.${language}.url`]: url,
        });
        if (!opportunity) throw new HttpException(404, MESSAGES.OPPORTUNITY.NOT_FOUND);

        return {
            slug: (opportunity.versions as any).get(targetLanguage).url,
        };
    }

    public async updateOpportunityVersion(
        opportunityId: string,
        language: Language,
        versionData: Partial<OpportunityVersion>,
    ): Promise<OpportunityI | null> {
        const existingOpportunity = await this.get(opportunityId);

        if (existingOpportunity.versions[language]) {
            return await this.updateOpportuniteBylanguageandId(language, opportunityId, versionData);
        } else {
            return await this.addVersionToOpportunity(opportunityId, {
                language: language,
                title: versionData.title as string,
                jobDescription: versionData.jobDescription as string,
                metaTitle: versionData.metaTitle,
                metaDescription: versionData.metaDescription,
                shareOnSocialMedia: versionData.shareOnSocialMedia as boolean,
                _id: new Types.ObjectId(),
                createdAt: new Date(),
                updatedAt: new Date(),
                visibility: versionData.visibility,
                createdBy: versionData.createdBy as string,
                isArchived: versionData.isArchived as boolean,
            } as any);
        }
    }


    private async fetchOpportunities() {
        try {
            const response = await axios.get(process.env.OPENING_LINK as string, {
                headers: {
                    Accept: 'application/json',
                    Cookie: `accessToken=${process.env.HUNTER_ACCESS_TOKEN}; refreshToken=${process.env.HUNTER_REFRESH_TOKEN}`,
                },
            });

            if (response.status !== 200 || !response.data.length) return [];
            return response.data;
        } catch (error: any) {
            console.error('Error fetching opportunities: ', error.message);
            return [];
        }
    }

    private mapIndustry(industry: string): string {
        const industries: Record<string, string> = {
            'IT & TELECOM': 'It & Telecom',
            TRANSPORT: 'Transport',
            ENERGIES: 'Energies',
            OTHERS: 'Others',
            BANKING: 'Banking',
            PHARMACEUTICAL: 'Pharmaceutical',
        };
        return industries[industry] ?? 'Others';
    }

    private createJobUrl(jobTitle: string, reference: string): string {
        return `${jobTitle
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .trim()
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')}-${reference.toLowerCase()}`;
    }

    private logToFile(message: string, overwrite = false): void {
        const logDir = path.join(__dirname, '../../../../logs/cron_jobs/opportunity');
        const fileName = path.join(logDir, `${new Date().toISOString().substring(0, 10)}.log`);

        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }

        if (!fs.existsSync(fileName)) {
            fs.writeFileSync(fileName, '');
        }

        overwrite === true ? fs.writeFileSync(fileName, `${message}\n`) : fs.appendFileSync(fileName, `${message}\n`);
    }

    public async importOpportunitiesFromHunter() {
        try {
            const opportunities = await this.fetchOpportunities();
            if (!opportunities.length) return;

            this.logToFile(`Received ${opportunities.length} opportunities`, true);

            for (const opportunity of opportunities) {
                if (this.shouldSkipOpportunity(opportunity)) continue;

                const existingOpportunity = await this.Opportunity.findOne({ reference: opportunity.reference });
                const newOpportunity = await this.buildOpportunityData(opportunity, existingOpportunity);

                await this.saveOrUpdateOpportunity(existingOpportunity, newOpportunity, opportunity.reference);
                await delay(2000);
            }

            this.updateOpportunitySitemap();
        } catch (error: any) {
            console.error('Error importing opportunities: ', error.message);
        }
    }

    private shouldSkipOpportunity(opportunity: any): boolean {
        const { project, compensation, reference } = opportunity;
        if (!compensation?.recruiters?.length && project.opportunityType !== OpportunityType.IN_HOUSE) {
            this.logToFile(`Skipping ${reference}: No recruiters assigned`);
            return true;
        }

        if (project?.opportunityType === 'Capability') {
            this.logToFile(`Skipping ${reference}: Capability`);
            return true;
        }

        return false;
    }

    private async buildOpportunityData(opportunity: any, existingOpportunity: any): Promise<any> {
        const { project, requirement, reference } = opportunity;

        const jobDescriptions = await this.getJobDescriptions(project, requirement, existingOpportunity);

        return {
            versions: this.buildVersions(project, reference, jobDescriptions, opportunity),
            opportunityType: project.opportunityType,
            dateOfExpiration: project.deadline ? new Date(project.deadline) : new Date(new Date().setMonth(new Date().getMonth() + 3)),
            industry: project.openingIndustry ? this.mapIndustry(project.openingIndustry) : this.mapIndustry(project.industry),
            country: project.country,
            reference,
            contractType: project.contractType.join(' ').trim(),
            urgent: opportunity.isUrgent,
            minExperience: Number(requirement?.yearsOfExperience?.split('-')[0]) || 2,
            maxExperience: Number(requirement?.yearsOfExperience?.split('-')[1]) || 15,
        };
    }

    private async getJobDescriptions(project: any, requirement: any, existingOpportunity: any): Promise<any> {
        return {
            fr: existingOpportunity
                ? existingOpportunity?.versions['fr']?.jobDescription ?? existingOpportunity.versions.get('fr').jobDescription
                : await generateJobDescription(
                    this.mapIndustry(project.industry),
                    project.jobTitle,
                    Number(requirement?.yearsOfExperience?.split('-')[0]),
                    'fr',
                ),
            en: existingOpportunity
                ? existingOpportunity?.versions['en']?.jobDescription ?? existingOpportunity.versions.get('en').jobDescription
                : await generateJobDescription(
                    this.mapIndustry(project.industry),
                    project.jobTitle,
                    Number(requirement?.yearsOfExperience?.split('-')[0]),
                ),
        };
    }


    private buildVersions(project: any, reference: string, jobDescriptions: any, opportunity: any): any {
        return {
            fr: {
                alt: this.createJobUrl(project.jobTitle, reference),
                language: Language.FRENCH,
                title: project.jobTitle,
                jobDescription: jobDescriptions['fr'],
                shareOnSocialMedia: opportunity.toPublishOnSocialMedia,
                url: this.createJobUrl(project.jobTitle, reference),
                metaTitle: `Apply now for the ${project.jobTitle} job.`,
                metaDescription: `Explore the ${project.jobTitle} opportunity.`,
                createdAt: opportunity.createdAt,
                updatedAt: opportunity.updatedAt,
                visibility: Visibility.Public,
            },
            en: {
                alt: this.createJobUrl(project.jobTitle, reference),
                language: Language.ENGLISH,
                title: project.jobTitle,
                jobDescription: jobDescriptions['en'],
                shareOnSocialMedia: opportunity.toPublishOnSocialMedia,
                url: this.createJobUrl(project.jobTitle, reference),
                metaTitle: `Apply now for the ${project.jobTitle} job.`,
                metaDescription: `Explore the ${project.jobTitle} opportunity.`,
                createdAt: opportunity.createdAt,
                updatedAt: opportunity.updatedAt,
                visibility: Visibility.Public,
            },
        };
    }

    private async saveOrUpdateOpportunity(existingOpportunity: any, newOpportunity: any, reference: string): Promise<void> {
        if (existingOpportunity) {
            await this.Opportunity.findByIdAndUpdate(existingOpportunity._id, { $set: newOpportunity });
            this.logToFile(`Updated ${reference}`);
        } else {
            await this.Opportunity.create(newOpportunity);
            this.logToFile(`Created ${reference}`);
        }
    }
    public async updateOpportunitySitemap(newOpportunity?: any) {
        try {
            const staticUrls = [
                {
                    loc: 'https://www.pentabell.com/opportunities/',
                    lastmod: new Date().toISOString(),
                    alternates: [
                        { hreflang: 'en-US', href: 'https://www.pentabell.com/opportunities/' },
                        { hreflang: 'fr-FR', href: 'https://www.pentabell.com/fr/opportunities/' },
                    ],
                },
                {
                    loc: 'https://www.pentabell.com/fr/opportunities/',
                    lastmod: new Date().toISOString(),
                    alternates: [
                        { hreflang: 'en-US', href: 'https://www.pentabell.com/opportunities/' },
                        { hreflang: 'fr-FR', href: 'https://www.pentabell.com/fr/opportunities/' },
                    ],
                },
            ];

            const openings: any = newOpportunity ? [...(await this.Opportunity.find()), newOpportunity] : await this.Opportunity.find();

            let sitemapContent = '<?xml version="1.0" encoding="UTF-8"?>\n';
            sitemapContent += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"\n';
            sitemapContent += '        xmlns:xhtml="http://www.w3.org/1999/xhtml">\n';

            staticUrls.forEach(entry => {
                sitemapContent += '  <url>\n';
                sitemapContent += `    <loc>${entry.loc}</loc>\n`;
                sitemapContent += `    <lastmod>${entry.lastmod}</lastmod>\n`;
                sitemapContent += `    <xhtml:link rel='canonical' href='${entry.loc}' />\n`;
                entry.alternates.forEach(alt => {
                    sitemapContent += `    <xhtml:link rel='alternate' hreflang='${alt.hreflang}' href='${alt.href}' />\n`;
                });
                sitemapContent += '  </url>\n';
            });

            console.log(openings[0].versions.get('fr'));

            openings.forEach((opening: any) => {
                ['en', 'fr'].forEach(language => {
                    if (
                        opening.versions?.get(language)?.url &&
                        !opening.versions?.get(language)?.isArchived &&
                        opening.versions?.get(language)?.visibility === Visibility.Public
                    ) {
                        sitemapContent += '  <url>\n';
                        sitemapContent += `    <loc>https://www.pentabell.com/${language === 'fr' ? 'fr/' : ''}opportunities/${opening.versions?.get(language)?.url
                            }/</loc>\n`;
                        sitemapContent += `    <lastmod>${new Date(opening.versions?.get(language)?.updatedAt).toISOString()}</lastmod>\n`;
                        sitemapContent += `    <xhtml:link rel='canonical' href='https://www.pentabell.com/${language === 'fr' ? 'fr/' : ''
                            }opportunities/${opening.versions?.get(language)?.url}/' />\n`;
                        ['en', 'fr'].forEach(altLang => {
                            if (
                                opening.versions?.get(language)?.url &&
                                !opening.versions?.get(language)?.isArchived &&
                                opening.versions?.get(language)?.visibility === Visibility.Public
                            ) {
                                sitemapContent += `    <xhtml:link rel='alternate' hreflang='${altLang === 'en' ? 'en-US' : 'fr-FR'
                                    }' href='https://www.pentabell.com/${altLang === 'fr' ? 'fr/' : ''}opportunities/${opening.versions?.get(language)?.url
                                    }/' />\n`;
                            }
                        });
                        sitemapContent += '  </url>\n';
                    }
                });
            });

            sitemapContent += '</urlset>';

            const filePath = path.join(__dirname, '../../../../../client/public/sitemap_opportunity.xml');
            fs.writeFileSync(filePath, sitemapContent, 'utf-8');
            console.log(`Sitemap has been generated at ${filePath}`);
            console.log(openings.length > 1 ? `With ${openings.length} opportunities` : `With ${openings.length} opportunity`);
        } catch (error: any) {
            console.error('Error creating opportunity sitemap: ', error.message);
        }
    }

    public async updateJobDescriptions() {
        const openings: any = await this.Opportunity.find({
            $or: [{ 'versions.fr.jobDescription': /N\/A/i }, { 'versions.en.jobDescription': /N\/A/i }],
        });
        console.log(`Found ${openings.length} openings needing job descriptions`);

        for (const opening of openings) {
            try {
                opening.versions.get('fr').jobDescription = await generateJobDescription(
                    opening.industry,
                    opening.versions.get('fr').title,
                    opening.minExperience,
                    'fr',
                );
                console.log(
                    `Updated JD for ${opening.versions.get('fr').language} version of (${opening.reference}) (${opening.versions.get('fr').title})`,
                );
                opening.versions.get('en').jobDescription = await generateJobDescription(
                    opening.industry,
                    opening.versions.get('en').title,
                    opening.minExperience,
                    'en',
                );
                console.log(
                    `Updated JD for ${opening.versions.get('en').language} version of (${opening.reference}) (${opening.versions.get('en').title})`,
                );
            } catch (err: any) {
                console.error(`Failed to generate JD for (${opening.reference}): ${err.message}`);
            }

            await opening.save();
        }
    }
}

export default OpportunityService;
