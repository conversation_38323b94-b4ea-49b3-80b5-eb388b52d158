import Locations from "@/components/sections/Locations";
import OfficesListForContactPage from "@/components/sections/OfficesListForContactPage";
import ContactPageForm from "@/features/forms/components/ContactPageForm";
import initTranslations from "@/app/i18n";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }contact/`;

  const { t } = await initTranslations(locale, ["global"]);

  const languages = {
    fr: `https://www.pentabell.com/fr/contact/`,
    en: `https://www.pentabell.com/contact/`,
    "x-default": `https://www.pentabell.com/contact/`,
  };
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/contact`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("global:contactPage:metaTitle"),
    description: t("global:contactPage:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
function contact() {
  return (
    <div>
      <OfficesListForContactPage />
      <Locations />
      <ContactPageForm />
    </div>
  );
}
export default contact;
