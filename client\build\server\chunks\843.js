"use strict";exports.id=843,exports.ids=[843],exports.modules={20843:(e,t,s)=>{let n;function r(e){let t="";return e&&"string"==typeof e&&(t=e.toLowerCase().replace(/[^a-z0-9\s]/g,"").trim().replace(/\s{2,}/g," ").replace(/\s/g,"-")),t}s.d(t,{o:()=>r});let o=new Set;function i(e){o.forEach(t=>{t.c(e)||(o.delete(t),t.f())}),0!==o.size&&raf(i)}let l="undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:global;class d{_listeners="WeakMap"in l?new WeakMap:void 0;_observer=void 0;options;constructor(e){this.options=e}observe(e,t){return this._listeners.set(e,t),this._getObserver().observe(e,this.options),()=>{this._listeners.delete(e),this._observer.unobserve(e)}}_getObserver(){return this._observer??(this._observer=new ResizeObserver(e=>{for(let t of e)d.entries.set(t.target,t),this._listeners.get(t.target)?.(t)}))}}d.entries="WeakMap"in l?new WeakMap:void 0;function a(e){e.parentNode&&e.parentNode.removeChild(e)}function p(e){return e()}let f=[],u=[],v=[],w=[],c=!1;function h(e){v.push(e)}let _=new Set,b=0;"function"==typeof HTMLElement&&HTMLElement,"undefined"!=typeof window&&(window.__svelte||(window.__svelte={v:new Set})).v.add("4")}};