{"version": 3, "file": "authentication.middleware.js", "sourceRoot": "", "sources": ["../../src/middlewares/authentication.middleware.ts"], "names": [], "mappings": ";;;;;;AACA,+CAAwC;AACxC,uFAA8D;AAE9D,MAAM,eAAe,GAAG,KAAK,EAAE,OAAgB,EAAE,QAAkB,EAAE,IAAkB,EAAE,EAAE;IACvF,IAAI,CAAC,OAAO,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC,IAAI,wBAAa,CAAC,GAAG,EAAE,6BAA6B,CAAC,CAAC,CAAC;IACtF,OAAO,IAAI,EAAE,CAAC;AAClB,CAAC,CAAC;AAEK,MAAM,oBAAoB,GAAG,KAAK,EAAE,OAAgB,EAAE,QAAkB,EAAE,IAAkB,EAAE,EAAE;IACnG,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC;IAEtD,IAAI,CAAC,WAAW,IAAI,CAAC,YAAY;QAAE,OAAO,IAAI,EAAE,CAAC;IACjD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,MAAM,eAAI,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC;IAC/G,IAAI,OAAO,EAAE,CAAC;QACV,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC;QACvB,OAAO,IAAI,EAAE,CAAC;IAClB,CAAC;IACD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO,IAAI,YAAY,IAAI,CAAC,MAAM,eAAI,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;IAE9I,IAAI,OAAO,EAAE,CAAC;QACV,MAAM,WAAW,GAAG,MAAM,eAAI,CAAC,aAAa,CACxC,EAAE,GAAG,EAAE,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,EAAE,EAC1C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,EAC5C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CACxC,CAAC;QAEF,QAAQ,CAAC,MAAM,CAAC,aAAa,EAAE,WAAW,EAAE;YACxC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC;YAC1C,QAAQ,EAAE,IAAI;SACjB,CAAC,CAAC;QAEH,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,eAAI,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,OAAc,CAAC;QAClH,OAAO,IAAI,EAAE,CAAC;IAClB,CAAC;AACL,CAAC,CAAC;AA1BW,QAAA,oBAAoB,wBA0B/B;AAEF,kBAAe,eAAe,CAAC"}