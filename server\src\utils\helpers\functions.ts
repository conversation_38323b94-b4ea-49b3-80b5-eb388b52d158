import { UserI } from '@/apis/user/user.interfaces';
import { Role } from './constants';
import { NotificationI } from '@/apis/notifications/notification.interface';
import userModel from '@/apis/user/user.model';
import { OpportunityI } from '@/apis/opportunity/opportunity.interface';
import { sendNotification } from '../config/socket';
import WebSocket from 'ws';
import userSettingsModel from '@/apis/settings/settings.model';
import alertModel from '@/apis/alert/alert.model';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import articleCategoryModel from '@/apis/article/category/article.category.model';
import { ObjectId } from 'mongodb';
import validator from 'validator';
import WordExtractor from 'word-extractor';
import pdf from 'pdf-parse';
import mammoth from 'mammoth';
import fs from 'fs';
import https from 'https';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import FilesService from '@/apis/storage/files.service';
import { FileI } from '@/apis/storage/files.interface';
import axios from 'axios';
import FormData from 'form-data';
import guideCategoryModel from '@/apis/guide/categoryguide/guide.category.model';
import { MESSAGES } from './messages';
import HttpException from '../exceptions/http.exception';

const User = userModel;
const Alert = alertModel;
const Category = articleCategoryModel;
const Settings = userSettingsModel;

const MAX_RETRIES = 5;
const RETRY_DELAY_MS = 2000;

export async function getCategoryUrlByIdVersion(id: ObjectId): Promise<any | null> {
    try {
        const category = await Category.findOne({ 'versionscategory._id': id }).exec();

        if (category) {
            const version = category.versionscategory.find((v: any) => v._id.toString() === id.toString());
            return version ? version : null;
        }

        return null;
    } catch (error) {
       throw new HttpException (500,MESSAGES.GENERAL.SERVER_ERROR);
        return null;
    }
}

export async function getCategoryGuideUrlByIdVersion(id: ObjectId): Promise<any | null> {
    try {
        const category = await guideCategoryModel.findOne({ 'categoryguide._id': id }).exec();

        if (category) {
            const version = category.categoryguide.find((v: any) => v._id.toString() === id.toString());
            return version ? version : null;
        }

        return null;
    } catch (error: unknown) {
        console.error('Error fetching category URL:', error);
        return null;
    }
}

export async function getServiceGuideUrlByIdVersion(id: ObjectId): Promise<any | null> {
    try {
        const category = await Category.findOne({ 'versionscategory._id': id }).exec();

        if (category) {
            const version = category.versionscategory.find((v: any) => v._id.toString() === id.toString());
            return version ? version : null;
        }

        return null;
    } catch (error: unknown) {
        console.error('Error fetching category URL:', error);
        return null;
    }
}

export function verifyRole(role: Role, currentUser: UserI): boolean {
    return currentUser.roles.indexOf(role) === -1 ? false : true;
}
export function validateEmailFormat(email: string): void {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new HttpException(400, MESSAGES.USER.EMAIL_INVALID);
  }
}

export function formatDate(date: Date): string {
    const year: number = date.getFullYear();
    const month: string = String(date.getMonth() + 1).padStart(2, '0');
    const day: string = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

export async function mapNotifications(notifications: NotificationI[]) {
    const mappedNotifications: any = [];
    for (const notification of notifications) {
        const user = await User.findById(notification.sender);
        mappedNotifications.push({
            _id: notification._id,
            image: user ? `${process.env.STORAGE_URL as string}/${user.profilePicture}` : undefined,
            message: notification.message,
            detailPage: notification.link,
            receivedTime: notification.createdAt,
            isRead: notification.isRead,
            sender: user ? `${user?.firstName} ${user?.lastName?.toUpperCase()}` : null,
            type: notification.type,
            link: notification.link,
        });
    }
    return mappedNotifications;
}

export async function getConnectedUser(refreshToken: string) {
    if (!refreshToken) return 'Guest User';
    const payload = jwt.verify(refreshToken, String(process.env.REFRESH_TOKEN_PRIVATE_KEY));
    if (!payload) return 'Guest User';
    const connectedUser: UserI | null = await User.findById(payload);

    return connectedUser?.firstName ? `${connectedUser?.firstName} ${connectedUser?.lastName?.toUpperCase()}` : 'Guest User';
}

export function computeChecksumFromBuffer(buffer: string, algorithm = 'sha256') {
    const hash = crypto.createHash(algorithm);
    hash.update(buffer);
    return hash.digest('hex');
}

export async function sendAlert(opportunite: OpportunityI) {
    const alerts = await Alert.find({
        isActive: true,
        industry: opportunite.industry,
        country: String(opportunite.country),
    });

    if (!alerts || alerts.length === 0) {
        console.log('No active alerts found for this opportunity.');
        return;
    }

    await Promise.all(
        alerts.map(async alert => {
            const userSettings = await Settings.findOne({ user: alert.createdBy }).lean();

            if (userSettings?.notifications?.newJobAlerts?.website === true) {
                console.log(`Sending job alert to user: ${alert.createdBy}`);

                const message = 'New job opportunity';
                const description = `${opportunite.versions['en'].title} in ${String(opportunite.country)},${String(opportunite.industry)}`;
                const link = `/opportunities/${opportunite.versions['en'].url}`;

                await sendNotification({
                    receiver: alert.createdBy,
                    type: 'jobalert',
                    message,
                    description,
                    link,
                });
            } else {
                console.log(`User ${alert.createdBy} has disabled website job alerts.`);
            }
        }),
    );
}

export function computeChecksumFromStream(stream: any): Promise<string> {
    return new Promise((resolve, reject) => {
        const hash = crypto.createHash('sha256');

        stream.on('data', (data: any) => {
            hash.update(data);
        });

        stream.on('end', () => {
            const checksum = hash.digest('hex');
            resolve(checksum);
        });

        stream.on('error', reject);
    });
}

export function consoleLog(...args: any) {
    if ((process.env.NODE_ENV as string) !== 'prod') {
        console.log(args.toString().replace(',', ' '));
    }
}

export function delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
}

export function parseFrenchDate(frenchDateStr: string, timeStr: string) {
    const frenchToEnglishMonths: any = {
        janvier: 'January',
        février: 'February',
        mars: 'March',
        avril: 'April',
        mai: 'May',
        juin: 'June',
        juillet: 'July',
        août: 'August',
        septembre: 'September',
        octobre: 'October',
        novembre: 'November',
        décembre: 'December',
    };

    const englishDateStr = frenchDateStr.replace(
        /janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre/,
        (match: string) => frenchToEnglishMonths[match.toLowerCase()],
    );

    const combinedDateTimeStr = `${englishDateStr} ${timeStr}`;

    return new Date(combinedDateTimeStr);
}

export function validate(input: string) {
    if (input.length === 0) return false;

    if (input.includes('INTERNAL REQUISITION FORM')) return false;

    const keywordsFr = ['formation', 'expériences', 'expérience', 'compétences', 'compétence', 'résumé', 'contact', 'objectif', 'projets académique'];
    const keywordsEn = ['education', 'experiences', 'experience', 'skills', 'skill', 'summary', 'contact', 'objective', 'academic projects'];

    const allKeywords = [...keywordsFr, ...keywordsEn];
    const pattern = new RegExp(allKeywords.join('|'), 'i');
    return pattern.test(input);
}

export const extractEmails = (text: string) => {
    const words = text.split(/\s+/);

    return words.filter(word => validator.isEmail(word));
};

export async function extractTextFromFile(contentType: string, file: Buffer) {
    if (contentType === 'application/pdf') {
        const data = await pdf(file);
        return data.text;
    } else if (contentType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        const textResult = await mammoth.extractRawText({ buffer: file });
        return textResult.value;
    } else if (contentType === 'application/msword') {
        const extractor = new WordExtractor();
        const doc = await extractor.extract(file);
        return doc.getBody();
    } else return;
}

export function calculateChecksum(filePathOrUrl: string): Promise<string> {
    return new Promise((resolve, reject) => {
        const hash = crypto.createHash('sha256');

        if (filePathOrUrl.startsWith('http://') || filePathOrUrl.startsWith('https://')) {
            https
                .get(filePathOrUrl, response => {
                    if (response.statusCode !== 200) {
                        return reject(new Error(`Failed to fetch resource. Status code: ${response.statusCode}`));
                    }
                    response.on('data', chunk => hash.update(chunk));
                    response.on('end', () => resolve(hash.digest('hex')));
                    response.on('error', err => reject(err));
                })
                .on('error', err => reject(err));
        } else {
            const fileStream = fs.createReadStream(filePathOrUrl);
            fileStream.on('data', chunk => hash.update(chunk));
            fileStream.on('end', () => resolve(hash.digest('hex')));
            fileStream.on('error', err => reject(err));
        }
    });
}

export async function downloadGoogleProfilePhoto(imageUrl: string, foundUser: any) {
    const folderPath = path.join(__dirname, `../../../uploads`, 'users', new Date().getFullYear().toString());
    if (!fs.existsSync(folderPath)) fs.mkdirSync(folderPath, { recursive: true });

    const uuid = uuidv4().replace(/-/g, '');
    const filesService = new FilesService();

    const fileName = `${uuid}.jpg`;
    const filePath = path.join(folderPath, fileName);
    const urlParts = imageUrl.split('/');
    const originalName = decodeURIComponent(urlParts[urlParts.length - 1]);
    https
        .get(imageUrl, async response => {
            if (response.statusCode === 200) {
                const checksum: any = await calculateChecksum(imageUrl);
                const existingFile = await filesService.getFileByChecksum(checksum);

                if (existingFile) {
                    await userModel.findByIdAndUpdate(foundUser._id, { profilePicture: existingFile.fileName });
                } else {
                    const file = fs.createWriteStream(filePath);
                    const fileType: any = response.headers['content-type'];
                    response.pipe(file);

                    file.on('finish', async () => {
                        file.close();
                        const fileSize = fs.statSync(filePath).size;
                        const fileData: FileI = {
                            resource: 'users',
                            folder: new Date().getFullYear().toString(),
                            uuid,
                            fileName,
                            fileType,
                            originalName,
                            fileSize,
                            checksum,
                        };
                        await filesService.createFile(fileData);
                        console.log(`Profile photo saved as: ${filePath}`);
                    });

                    await userModel.findByIdAndUpdate(foundUser._id, { profilePicture: fileName });
                }
            } else {
                console.error(`Failed to download profile photo. Status code: ${response.statusCode}`);
            }
        })
        .on('error', (err: any) => {
            fs.unlink(filePath, () => { });
            console.error(`Error downloading image: ${err.message}`);
        });
}

export function verifyRoles(userRoles: Role[], requiredRoles: Role[]): boolean {
    return requiredRoles.every(role => userRoles.includes(role));
}

export async function extractTextFromScannedPDF(dataBuffer: any) {
    const formData = new FormData();
    formData.append('file', dataBuffer, {
        filename: 'test.pdf',
        contentType: 'multipart/form-data',
    });

    const response = await axios.post(process.env.EXTRACT_TEXT_FROM_PDF as string, formData, {
        headers: {
            ...formData?.getHeaders(),
        },
    });

    return response?.data?.text;
}

export function formatJobDetails(details: any, language = 'en'): string {
    if (!details) return '';

    const createSection = (title: string, content: string) => `<strong>${title}:</strong><br>${content}<br><br>`;
    const createList = (items: any[]) => items?.map(item => `&mdash; ${item}<br>`).join('') || '';

    let html = '';
    html += createSection(language === 'fr' ? "Aperçu de l'entreprise" : 'Company overview', details?.company_overview || 'N/A');
    html += createSection('Position', details?.job_overview?.position || 'N/A');
    html += createSection(language === 'fr' ? 'Description du poste' : 'Summary', details?.job_overview?.summary || 'N/A');
    html += `<strong>${language === 'fr' ? 'Responsabilités' : 'Responsibilities'}:</strong><br>${createList(details?.responsibilities)}<br>`;
    html += `<strong>${language === 'fr' ? 'Exigences du candidat:' : 'Requirements:'}</strong><br>
    &mdash; ${details?.candidate_requirements?.education || 'N/A'}<br>
    &mdash; ${details?.candidate_requirements?.experience || 'N/A'}<br>${createList(details?.candidate_requirements?.skills)}`;

    return html;
}

export async function generateJobDescription(
    industry: string,
    jobTitle: string,
    minExperience: number,
    jobDescription: string,
    location: string,
    language = 'en',
    attempt = 1,
): Promise<string> {
    return new Promise((resolve, reject) => {
        const socket = new WebSocket(process.env.JOB_DESCRIPTION_GENERATOR as string);
        const messageBuffer: any[] = [];
        let dataReceived = false;
        let timeout: NodeJS.Timeout;

        const startInactivityTimer = () => {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                socket.close();
            }, 25000);
        };

        socket.on('open', () => {
            socket.send(
                JSON.stringify({
                    input: {
                        industry,
                        job_title: jobTitle,
                        jobDescription,
                        location,
                        language,
                        number_responsibilities: 5,
                        number_requirements: 5,
                        min_experience: minExperience,
                    },
                }),
            );

            startInactivityTimer();
        });

        socket.on('message', (data: any) => {
            try {
                messageBuffer.push(JSON.parse(data.toString('utf-8')));
                dataReceived = true;
                startInactivityTimer();
            } catch (error) {
                clearTimeout(timeout);
                socket.close();
                reject(new Error('Invalid WebSocket message ' + error));
            }
        });

        socket.on('close', async () => {
            clearTimeout(timeout);
            if (dataReceived && messageBuffer.length) {
                const finalData = messageBuffer.reduce((acc, part) => ({ ...acc, ...part }), {});
                resolve(formatJobDetails(finalData, language));
            } else {
                if (attempt < MAX_RETRIES) {
                    console.warn(`Retrying WebSocket (Attempt ${attempt + 1})...`);
                    await delay(RETRY_DELAY_MS);
                    resolve(generateJobDescription(industry, jobTitle, minExperience, jobDescription, location, language, attempt + 1));
                } else {
                    reject(new Error('No data received after maximum retries'));
                }
            }
        });

        socket.on('error', error => {
            clearTimeout(timeout);
            reject(new Error(`WebSocket error: ${error.message}`));
        });
    });
}

export async function translateJobTitle(job_title: string, target_language: 'en' | 'fr') {
    try {
        const response = await axios.post(process.env.JOB_TITLE_TRANSLATOR as string, {
            job_title,
            target_language,
        });
        return response.data.translated_title;
    } catch (err) {
        console.error(err);
        return null;
    }
}
