"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(dashboard)/backoffice/blogs/add/page",{

/***/ "(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx":
/*!***********************************************************!*\
  !*** ./src/features/blog/components/DocumentImporter.jsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(app-pages-browser)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var mammoth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mammoth */ \"(app-pages-browser)/./node_modules/mammoth/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,LinearProgress,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Close.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Close_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Close!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Inject drag state styles\nconst dragActiveStyles = `\n  .file-labels.drag-active {\n    border-color: #1976d2 !important;\n    background-color: rgba(25, 118, 210, 0.04) !important;\n  }\n  .file-labels.disabled {\n    cursor: not-allowed !important;\n    opacity: 0.6 !important;\n  }\n`;\nif (typeof document !== \"undefined\" && !document.getElementById(\"document-importer-styles\")) {\n    const styleSheet = document.createElement(\"style\");\n    styleSheet.id = \"document-importer-styles\";\n    styleSheet.textContent = dragActiveStyles;\n    document.head.appendChild(styleSheet);\n}\nconst DocumentImporter = (param)=>{\n    let { onContentExtracted, onMetadataExtracted, language = \"EN\", disabled = false } = param;\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [previewOpen, setPreviewOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [extractedData, setExtractedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const acceptedFileTypes = {\n        \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\": [\n            \".docx\"\n        ],\n        \"application/msword\": [\n            \".doc\"\n        ],\n        \"application/pdf\": [\n            \".pdf\"\n        ],\n        \"text/plain\": [\n            \".txt\"\n        ]\n    };\n    const extractMetadataFromContent = (htmlContent)=>{\n        const tempDiv = document.createElement(\"div\");\n        tempDiv.innerHTML = htmlContent;\n        const headings = tempDiv.querySelectorAll(\"h1, h2, h3, strong\");\n        const potentialTitle = headings.length > 0 ? headings[0].textContent.trim() : \"\";\n        const paragraphs = tempDiv.querySelectorAll(\"p\");\n        const potentialDescription = paragraphs.length > 0 ? paragraphs[0].textContent.trim().substring(0, 160) : \"\";\n        const keywords = Array.from(headings).map((h)=>h.textContent.trim()).filter((text)=>text.length > 2 && text.length < 50).slice(0, 10);\n        return {\n            title: potentialTitle,\n            description: potentialDescription,\n            keywords\n        };\n    };\n    const processWordDocument = async (file)=>{\n        try {\n            setProgress(25);\n            const arrayBuffer = await file.arrayBuffer();\n            setProgress(50);\n            const result = await mammoth__WEBPACK_IMPORTED_MODULE_3__.convertToHtml({\n                arrayBuffer,\n                options: {\n                    styleMap: [\n                        \"p[style-name='Heading 1'] => h1:fresh\",\n                        \"p[style-name='Heading 2'] => h2:fresh\",\n                        \"p[style-name='Heading 3'] => h3:fresh\",\n                        \"p[style-name='Title'] => h1.title:fresh\",\n                        \"p[style-name='Subtitle'] => h2.subtitle:fresh\"\n                    ],\n                    includeDefaultStyleMap: true,\n                    convertImage: mammoth__WEBPACK_IMPORTED_MODULE_3__.images.imgElement((image)=>image.read(\"base64\").then((imageBuffer)=>({\n                                src: `data:${image.contentType};base64,${imageBuffer}`\n                            })))\n                }\n            });\n            setProgress(75);\n            const cleanContent = result.value.replace(/<p><\\/p>/g, \"\").replace(/\\s+/g, \" \").trim();\n            const metadata = extractMetadataFromContent(cleanContent);\n            setProgress(100);\n            return {\n                content: cleanContent,\n                metadata,\n                warnings: result.messages || []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process Word document: ${error.message}`);\n        }\n    };\n    const processTextFile = async (file)=>{\n        try {\n            setProgress(50);\n            const text = await file.text();\n            const htmlContent = text.split(\"\\n\").map((line)=>line.trim()).filter((line)=>line.length > 0).map((line)=>`<p>${line}</p>`).join(\"\");\n            const metadata = extractMetadataFromContent(htmlContent);\n            setProgress(100);\n            return {\n                content: htmlContent,\n                metadata,\n                warnings: []\n            };\n        } catch (error) {\n            throw new Error(`Failed to process text file: ${error.message}`);\n        }\n    };\n    const processFile = async (file)=>{\n        setIsProcessing(true);\n        setProgress(0);\n        setError(null);\n        setSuccess(false);\n        try {\n            let result;\n            if (file.type.includes(\"wordprocessingml\") || file.type.includes(\"msword\")) {\n                result = await processWordDocument(file);\n            } else if (file.type === \"text/plain\") {\n                result = await processTextFile(file);\n            } else {\n                throw new Error(\"Unsupported file type\");\n            }\n            setExtractedData(result);\n            setSuccess(true);\n            setPreviewOpen(true);\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setIsProcessing(false);\n            setProgress(0);\n        }\n    };\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((acceptedFiles)=>{\n        if (acceptedFiles.length > 0) processFile(acceptedFiles[0]);\n    }, []);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: acceptedFileTypes,\n        maxFiles: 1,\n        disabled: disabled || isProcessing\n    });\n    const handleApplyContent = ()=>{\n        if (extractedData) {\n            onContentExtracted(extractedData.content);\n            if (onMetadataExtracted) {\n                onMetadataExtracted(extractedData.metadata);\n            }\n            setPreviewOpen(false);\n            setExtractedData(null);\n        }\n    };\n    const handleClosePreview = ()=>{\n        setPreviewOpen(false);\n        setExtractedData(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        sx: {\n            mb: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"label-form\",\n                children: [\n                    t(\"createArticle:importDocument\"),\n                    \" (\",\n                    language,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"upload-container\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    ...getRootProps(),\n                    className: `file-labels ${isDragActive ? \"drag-active\" : \"\"} ${disabled || isProcessing ? \"disabled\" : \"\"}`,\n                    style: {\n                        opacity: disabled || isProcessing ? 0.6 : 1,\n                        cursor: disabled || isProcessing ? \"not-allowed\" : \"pointer\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                            ...getInputProps(),\n                            className: \"file-input\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"upload-area\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"icon-pic\",\n                                    style: {\n                                        backgroundImage: `url(\"${upload}\")`,\n                                        backgroundSize: \"cover\",\n                                        backgroundRepeat: \"no-repeat\",\n                                        backgroundPosition: \"center\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-text\",\n                                            children: isDragActive ? t(\"createArticle:dropFileHere\") : t(\"createArticle:importFromDocument\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"upload-description\",\n                                            children: [\n                                                t(\"createArticle:supportedFormats\"),\n                                                \": .docx, .doc, .txt\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined),\n            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                sx: {\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        variant: \"body2\",\n                        children: [\n                            t(\"createArticle:processingDocument\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 256,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"determinate\",\n                        value: progress\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 255,\n                columnNumber: 9\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setError(null),\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 264,\n                columnNumber: 9\n            }, undefined),\n            success && !previewOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                severity: \"success\",\n                sx: {\n                    mt: 2\n                },\n                onClose: ()=>setSuccess(false),\n                children: t(\"createArticle:documentProcessedSuccessfully\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                open: previewOpen,\n                onClose: handleClosePreview,\n                maxWidth: \"md\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"h6\",\n                                    children: t(\"createArticle:previewExtractedContent\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: handleClosePreview,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        children: extractedData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            children: [\n                                extractedData.metadata.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    mb: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedTitle\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            children: extractedData.metadata.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 17\n                                }, undefined),\n                                extractedData.metadata.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    mb: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            color: \"primary\",\n                                            children: [\n                                                t(\"createArticle:extractedDescription\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"body2\",\n                                            children: extractedData.metadata.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    variant: \"subtitle2\",\n                                    color: \"primary\",\n                                    sx: {\n                                        mb: 1\n                                    },\n                                    children: [\n                                        t(\"createArticle:extractedContent\"),\n                                        \":\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    sx: {\n                                        maxHeight: 400,\n                                        overflow: \"auto\",\n                                        border: \"1px solid\",\n                                        borderColor: \"grey.300\",\n                                        p: 2,\n                                        borderRadius: 1,\n                                        backgroundColor: \"grey.50\"\n                                    },\n                                    dangerouslySetInnerHTML: {\n                                        __html: extractedData.content\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, undefined),\n                                extractedData.warnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    severity: \"warning\",\n                                    sx: {\n                                        mt: 2\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            variant: \"subtitle2\",\n                                            children: [\n                                                t(\"createArticle:conversionWarnings\"),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            children: extractedData.warnings.map((w, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: w.message\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                            lineNumber: 302,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                onClick: handleClosePreview,\n                                children: t(\"global:cancel\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_LinearProgress_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                onClick: handleApplyContent,\n                                variant: \"contained\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Close_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 24\n                                }, void 0),\n                                children: t(\"createArticle:applyContent\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                        lineNumber: 354,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\blog\\\\components\\\\DocumentImporter.jsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DocumentImporter, \"7cFeiPgqFhGSzHeGTiXZN3jgdSs=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone\n    ];\n});\n_c = DocumentImporter;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DocumentImporter);\nvar _c;\n$RefreshReg$(_c, \"DocumentImporter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/blog/components/DocumentImporter.jsx\n"));

/***/ })

});