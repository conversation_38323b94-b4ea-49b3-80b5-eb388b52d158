"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_locales_en_opportunities_json"],{

/***/ "(app-pages-browser)/./src/locales/en/opportunities.json":
/*!*******************************************!*\
  !*** ./src/locales/en/opportunities.json ***!
  \*******************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = /*#__PURE__*/JSON.parse('{"findMatch":"Find your perfect match","country":"Country","contractType":"Contract type","otherFilters":"Other filters","readMore":"Read more","search":"Search","searchBy":"Search by job, keyword, or company","noOpportunitiesFound":"No opportunities found","tryDifferentFilters":"Try adjusting your filters or search criteria","metaTitleOneOpportunity1":"Apply now for the","metaTitleOneOpportunity2":"job in","metaDescriptionOneOpportunity1":"Immediate openings for ","metaDescriptionOneOpportunity2":". Elevate your career and contribute to exciting projects!","metaDescriptionOneOpportunity3":"in"}');

/***/ })

}]);