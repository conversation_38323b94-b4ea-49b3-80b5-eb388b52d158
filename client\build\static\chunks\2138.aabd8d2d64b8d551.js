"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2138],{12138:function(e){e.exports=JSON.parse('{"findMatch":"Find your perfect match","country":"Country","contractType":"Contract type","otherFilters":"Other filters","readMore":"Read more","search":"Search","searchBy":"Search by job, keyword, or company","noOpportunitiesFound":"No opportunities found","tryDifferentFilters":"Try adjusting your filters or search criteria","metaTitleOneOpportunity1":"Apply now for the","metaTitleOneOpportunity2":"job in","metaDescriptionOneOpportunity1":"Immediate openings for ","metaDescriptionOneOpportunity2":". Elevate your career and contribute to exciting projects!","metaDescriptionOneOpportunity3":"in"}')}}]);