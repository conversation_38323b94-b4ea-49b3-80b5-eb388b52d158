"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_locales_en_listGlossary_json";
exports.ids = ["_ssr_src_locales_en_listGlossary_json"];
exports.modules = {

/***/ "(ssr)/./src/locales/en/listGlossary.json":
/*!******************************************!*\
  !*** ./src/locales/en/listGlossary.json ***!
  \******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"filterByWord":"Filter by word","listOfGlossaries":"Glossaries list","author":"Author","word":"Word","letter":"Letter","createdBy":"Created by","createdAt":"Created at","language":"Language","visibility":"Visibility","archived":"Archived","actions":"Actions","dateOfCreation":"Date of creation","filterByLetter":"Filter by letter","url":"Url","addGlossary":"Add Glossary","archivage":"Archiving","metaTitleArticle":"Top Insights & Expert Tips on ","metaTitleArticle2":"| Pentabell Blog","metaDescriptionArticle":"news and insights, brought to you by Pentabell. Your source for valuable analysis to empower your decision-making in the","metaDescriptionArticle2":"landscape.","archiveConfirmation":"Are you sure you want to archive the glossary \\"{{word}}\\"?","unarchiveConfirmation":"Are you sure you want to unarchive the glossary \\"{{word}}\\"?","deleteConfirmation":"Are you sure you want to delete the glossary \\"{{word}}\\"? This action cannot be undone."}');

/***/ })

};
;