"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5117],{15735:function(e,t,n){n.d(t,{Z:function(){return w}});var o=n(2265),r=n(61994),l=n(20801),a=n(82590),i=n(16210),s=n(76301),c=n(37053),u=n(79114),d=n(85657),p=n(3858),f=n(53410),m=n(94143),v=n(50738);function h(e){return(0,v.ZP)("MuiAlert",e)}let g=(0,m.Z)("MuiAlert",["root","action","icon","message","filled","colorSuccess","colorInfo","colorWarning","colorError","filledSuccess","filledInfo","filledWarning","filledError","outlined","outlinedSuccess","outlinedInfo","outlinedWarning","outlinedError","standard","standardSuccess","standardInfo","standardWarning","standardError"]);var Z=n(59832),y=n(32464),x=n(57437),C=(0,y.Z)((0,x.jsx)("path",{d:"M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2, 4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0, 0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z"}),"SuccessOutlined"),M=(0,y.Z)((0,x.jsx)("path",{d:"M12 5.99L19.53 19H4.47L12 5.99M12 2L1 21h22L12 2zm1 14h-2v2h2v-2zm0-6h-2v4h2v-4z"}),"ReportProblemOutlined"),S=(0,y.Z)((0,x.jsx)("path",{d:"M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),"ErrorOutline"),b=(0,y.Z)((0,x.jsx)("path",{d:"M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20, 12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10, 10 0 0,0 12,2M11,17H13V11H11V17Z"}),"InfoOutlined"),z=n(14625);let j=e=>{let{variant:t,color:n,severity:o,classes:r}=e,a={root:["root",`color${(0,d.Z)(n||o)}`,`${t}${(0,d.Z)(n||o)}`,`${t}`],icon:["icon"],message:["message"],action:["action"]};return(0,l.Z)(a,h,r)},$=(0,i.ZP)(f.Z,{name:"MuiAlert",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,t[n.variant],t[`${n.variant}${(0,d.Z)(n.color||n.severity)}`]]}})((0,s.Z)(e=>{let{theme:t}=e,n="light"===t.palette.mode?a._j:a.$n,o="light"===t.palette.mode?a.$n:a._j;return{...t.typography.body2,backgroundColor:"transparent",display:"flex",padding:"6px 16px",variants:[...Object.entries(t.palette).filter((0,p.Z)(["light"])).map(e=>{let[r]=e;return{props:{colorSeverity:r,variant:"standard"},style:{color:t.vars?t.vars.palette.Alert[`${r}Color`]:n(t.palette[r].light,.6),backgroundColor:t.vars?t.vars.palette.Alert[`${r}StandardBg`]:o(t.palette[r].light,.9),[`& .${g.icon}`]:t.vars?{color:t.vars.palette.Alert[`${r}IconColor`]}:{color:t.palette[r].main}}}}),...Object.entries(t.palette).filter((0,p.Z)(["light"])).map(e=>{let[o]=e;return{props:{colorSeverity:o,variant:"outlined"},style:{color:t.vars?t.vars.palette.Alert[`${o}Color`]:n(t.palette[o].light,.6),border:`1px solid ${(t.vars||t).palette[o].light}`,[`& .${g.icon}`]:t.vars?{color:t.vars.palette.Alert[`${o}IconColor`]}:{color:t.palette[o].main}}}}),...Object.entries(t.palette).filter((0,p.Z)(["dark"])).map(e=>{let[n]=e;return{props:{colorSeverity:n,variant:"filled"},style:{fontWeight:t.typography.fontWeightMedium,...t.vars?{color:t.vars.palette.Alert[`${n}FilledColor`],backgroundColor:t.vars.palette.Alert[`${n}FilledBg`]}:{backgroundColor:"dark"===t.palette.mode?t.palette[n].dark:t.palette[n].main,color:t.palette.getContrastText(t.palette[n].main)}}}})]}})),A=(0,i.ZP)("div",{name:"MuiAlert",slot:"Icon",overridesResolver:(e,t)=>t.icon})({marginRight:12,padding:"7px 0",display:"flex",fontSize:22,opacity:.9}),O=(0,i.ZP)("div",{name:"MuiAlert",slot:"Message",overridesResolver:(e,t)=>t.message})({padding:"8px 0",minWidth:0,overflow:"auto"}),k=(0,i.ZP)("div",{name:"MuiAlert",slot:"Action",overridesResolver:(e,t)=>t.action})({display:"flex",alignItems:"flex-start",padding:"4px 0 0 16px",marginLeft:"auto",marginRight:-8}),I={success:(0,x.jsx)(C,{fontSize:"inherit"}),warning:(0,x.jsx)(M,{fontSize:"inherit"}),error:(0,x.jsx)(S,{fontSize:"inherit"}),info:(0,x.jsx)(b,{fontSize:"inherit"})};var w=o.forwardRef(function(e,t){let n=(0,c.i)({props:e,name:"MuiAlert"}),{action:o,children:l,className:a,closeText:i="Close",color:s,components:d={},componentsProps:p={},icon:f,iconMapping:m=I,onClose:v,role:h="alert",severity:g="success",slotProps:y={},slots:C={},variant:M="standard",...S}=n,b={...n,color:s,severity:g,variant:M,colorSeverity:s||g},w=j(b),E={slots:{closeButton:d.CloseButton,closeIcon:d.CloseIcon,...C},slotProps:{...p,...y}},[L,P]=(0,u.Z)("root",{ref:t,shouldForwardComponentProp:!0,className:(0,r.Z)(w.root,a),elementType:$,externalForwardedProps:{...E,...S},ownerState:b,additionalProps:{role:h,elevation:0}}),[N,R]=(0,u.Z)("icon",{className:w.icon,elementType:A,externalForwardedProps:E,ownerState:b}),[F,T]=(0,u.Z)("message",{className:w.message,elementType:O,externalForwardedProps:E,ownerState:b}),[B,H]=(0,u.Z)("action",{className:w.action,elementType:k,externalForwardedProps:E,ownerState:b}),[_,V]=(0,u.Z)("closeButton",{elementType:Z.Z,externalForwardedProps:E,ownerState:b}),[W,D]=(0,u.Z)("closeIcon",{elementType:z.Z,externalForwardedProps:E,ownerState:b});return(0,x.jsxs)(L,{...P,children:[!1!==f?(0,x.jsx)(N,{...R,children:f||m[g]||I[g]}):null,(0,x.jsx)(F,{...T,children:l}),null!=o?(0,x.jsx)(B,{...H,children:o}):null,null==o&&v?(0,x.jsx)(B,{...H,children:(0,x.jsx)(_,{size:"small","aria-label":i,title:i,color:"inherit",onClick:v,...V,children:(0,x.jsx)(W,{fontSize:"small",...D})})}):null]})})},10926:function(e,t,n){n.d(t,{default:function(){return v}});var o=n(2265),r=n(61994),l=n(55825),a=n(41823),i=n(20443),s=n(49695),c=n(57437),u=n(56063),d=n(26792),p=n(22166);let f=(0,n(94143).Z)("MuiBox",["root"]),m=(0,d.Z)();var v=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{themeId:t,defaultTheme:n,defaultClassName:u="MuiBox-root",generateClassName:d}=e,p=(0,l.ZP)("div",{shouldForwardProp:e=>"theme"!==e&&"sx"!==e&&"as"!==e})(a.Z);return o.forwardRef(function(e,o){let l=(0,s.Z)(n),{className:a,component:f="div",...m}=(0,i.Z)(e);return(0,c.jsx)(p,{as:f,ref:o,className:(0,r.Z)(a,d?d(u):u),theme:t&&l[t]||l,...m})})}({themeId:p.Z,defaultTheme:m,defaultClassName:f.root,generateClassName:u.Z.generate})},11953:function(e,t,n){n.d(t,{Z:function(){return k}});var o=n(2265),r=n(61994),l=n(20801),a=n(82590),i=n(66183),s=n(32464),c=n(57437),u=(0,s.Z)((0,c.jsx)("path",{d:"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"}),"CheckBoxOutlineBlank"),d=(0,s.Z)((0,c.jsx)("path",{d:"M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.11 0 2-.9 2-2V5c0-1.1-.89-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}),"CheckBox"),p=(0,s.Z)((0,c.jsx)("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10H7v-2h10v2z"}),"IndeterminateCheckBox"),f=n(85657),m=n(34765),v=n(94143),h=n(50738);function g(e){return(0,h.ZP)("MuiCheckbox",e)}let Z=(0,v.Z)("MuiCheckbox",["root","checked","disabled","indeterminate","colorPrimary","colorSecondary","sizeSmall","sizeMedium"]);var y=n(16210),x=n(76301),C=n(3858),M=n(37053),S=n(17419),b=n(79114);let z=e=>{let{classes:t,indeterminate:n,color:o,size:r}=e,a={root:["root",n&&"indeterminate",`color${(0,f.Z)(o)}`,`size${(0,f.Z)(r)}`]},i=(0,l.Z)(a,g,t);return{...t,...i}},j=(0,y.ZP)(i.Z,{shouldForwardProp:e=>(0,m.Z)(e)||"classes"===e,name:"MuiCheckbox",slot:"Root",overridesResolver:(e,t)=>{let{ownerState:n}=e;return[t.root,n.indeterminate&&t.indeterminate,t[`size${(0,f.Z)(n.size)}`],"default"!==n.color&&t[`color${(0,f.Z)(n.color)}`]]}})((0,x.Z)(e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,variants:[{props:{color:"default",disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette.action.activeChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,a.Fq)(t.palette.action.active,t.palette.action.hoverOpacity)}}},...Object.entries(t.palette).filter((0,C.Z)()).map(e=>{let[n]=e;return{props:{color:n,disableRipple:!1},style:{"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette[n].mainChannel} / ${t.vars.palette.action.hoverOpacity})`:(0,a.Fq)(t.palette[n].main,t.palette.action.hoverOpacity)}}}}),...Object.entries(t.palette).filter((0,C.Z)()).map(e=>{let[n]=e;return{props:{color:n},style:{[`&.${Z.checked}, &.${Z.indeterminate}`]:{color:(t.vars||t).palette[n].main},[`&.${Z.disabled}`]:{color:(t.vars||t).palette.action.disabled}}}}),{props:{disableRipple:!1},style:{"&:hover":{"@media (hover: none)":{backgroundColor:"transparent"}}}}]}})),$=(0,c.jsx)(d,{}),A=(0,c.jsx)(u,{}),O=(0,c.jsx)(p,{});var k=o.forwardRef(function(e,t){let n=(0,M.i)({props:e,name:"MuiCheckbox"}),{checkedIcon:l=$,color:a="primary",icon:i=A,indeterminate:s=!1,indeterminateIcon:u=O,inputProps:d,size:p="medium",disableRipple:f=!1,className:m,slots:v={},slotProps:h={},...g}=n,Z=s?u:i,y=s?u:l,x={...n,disableRipple:f,color:a,indeterminate:s,size:p},C=z(x),k=h.input??d,[I,w]=(0,b.Z)("root",{ref:t,elementType:j,className:(0,r.Z)(C.root,m),shouldForwardComponentProp:!0,externalForwardedProps:{slots:v,slotProps:h,...g},ownerState:x,additionalProps:{type:"checkbox",icon:o.cloneElement(Z,{fontSize:Z.props.fontSize??p}),checkedIcon:o.cloneElement(y,{fontSize:y.props.fontSize??p}),disableRipple:f,slots:v,slotProps:{input:(0,S.Z)("function"==typeof k?k(x):k,{"data-indeterminate":s})}}});return(0,c.jsx)(I,{...w,classes:C})})},14625:function(e,t,n){n(2265);var o=n(32464),r=n(57437);t.Z=(0,o.Z)((0,r.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}),"Close")},59873:function(e,t,n){n.d(t,{Z:function(){return u}});var o=n(2265),r=n.t(o,2),l=n(3450),a=n(93826),i=n(42827);let s={...r}.useSyncExternalStore;function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{themeId:t}=e;return function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=(0,i.Z)();r&&t&&(r=r[t]||r);let c="undefined"!=typeof window&&void 0!==window.matchMedia,{defaultMatches:u=!1,matchMedia:d=c?window.matchMedia:null,ssrMatchMedia:p=null,noSsr:f=!1}=(0,a.Z)({name:"MuiUseMediaQuery",props:n,theme:r}),m="function"==typeof e?e(r):e;return(void 0!==s?function(e,t,n,r,l){let a=o.useCallback(()=>t,[t]),i=o.useMemo(()=>{if(l&&n)return()=>n(e).matches;if(null!==r){let{matches:t}=r(e);return()=>t}return a},[a,e,r,l,n]),[c,u]=o.useMemo(()=>{if(null===n)return[a,()=>()=>{}];let t=n(e);return[()=>t.matches,e=>(t.addEventListener("change",e),()=>{t.removeEventListener("change",e)})]},[a,n,e]);return s(u,c,i)}:function(e,t,n,r,a){let[i,s]=o.useState(()=>a&&n?n(e).matches:r?r(e).matches:t);return(0,l.Z)(()=>{if(!n)return;let t=n(e),o=()=>{s(t.matches)};return o(),t.addEventListener("change",o),()=>{t.removeEventListener("change",o)}},[e,n]),i})(m=m.replace(/^@media( ?)/m,""),u,d,p,f)}}c();var u=c({themeId:n(22166).Z})},64821:function(e,t,n){var o=n(13859),r=n(53731);t.isCompanyEmail=function(e){if(!r.validate(e))return!1;let t=e.split("@")[1];return!o.has(t)},t.isCompanyDomain=function(e){return!o.has(e)}},53731:function(e,t){var n=/^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/;t.validate=function(e){if(!e||e.length>254||!n.test(e))return!1;var t=e.split("@");return!(t[0].length>64||t[1].split(".").some(function(e){return e.length>63}))}},61984:function(e,t,n){n.d(t,{Z:function(){return r}});let o={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function r(e={}){let t,n,l,a;let i=null,s=0,c=!1,u=!1,d=!1,p=!1;function f(){if(!l){if(h()){d=!0;return}c||n.emit("autoplay:play"),function(){let{ownerWindow:e}=n.internalEngine();e.clearTimeout(s),s=e.setTimeout(C,a[n.selectedScrollSnap()]),i=new Date().getTime(),n.emit("autoplay:timerset")}(),c=!0}}function m(){l||(c&&n.emit("autoplay:stop"),function(){let{ownerWindow:e}=n.internalEngine();e.clearTimeout(s),s=0,i=null,n.emit("autoplay:timerstopped")}(),c=!1)}function v(){if(h())return d=c,m();d&&f()}function h(){let{ownerDocument:e}=n.internalEngine();return"hidden"===e.visibilityState}function g(){u||m()}function Z(){u||f()}function y(){u=!0,m()}function x(){u=!1,f()}function C(){let{index:e}=n.internalEngine(),o=e.clone().add(1).get(),r=n.scrollSnapList().length-1,l=t.stopOnLastSnap&&o===r;if(n.canScrollNext()?n.scrollNext(p):n.scrollTo(0,p),n.emit("autoplay:select"),l)return m();f()}return{name:"autoplay",options:e,init:function(i,s){n=i;let{mergeOptions:c,optionsAtMedia:u}=s,d=c(o,r.globalOptions);if(t=u(c(d,e)),n.scrollSnapList().length<=1)return;p=t.jump,l=!1,a=function(e,t){let n=e.scrollSnapList();return"number"==typeof t?n.map(()=>t):t(n,e)}(n,t.delay);let{eventStore:h,ownerDocument:C}=n.internalEngine(),M=!!n.internalEngine().options.watchDrag,S=function(e,t){let n=e.rootNode();return t&&t(n)||n}(n,t.rootNode);h.add(C,"visibilitychange",v),M&&n.on("pointerDown",g),M&&!t.stopOnInteraction&&n.on("pointerUp",Z),t.stopOnMouseEnter&&h.add(S,"mouseenter",y),t.stopOnMouseEnter&&!t.stopOnInteraction&&h.add(S,"mouseleave",x),t.stopOnFocusIn&&n.on("slideFocusStart",m),t.stopOnFocusIn&&!t.stopOnInteraction&&h.add(n.containerNode(),"focusout",f),t.playOnInit&&f()},destroy:function(){n.off("pointerDown",g).off("pointerUp",Z).off("slideFocusStart",m),m(),l=!0,c=!1},play:function(e){void 0!==e&&(p=e),f()},stop:function(){c&&m()},reset:function(){c&&f()},isPlaying:function(){return c},timeUntilNext:function(){return i?a[n.selectedScrollSnap()]-(new Date().getTime()-i):null}}}r.globalOptions=void 0}}]);