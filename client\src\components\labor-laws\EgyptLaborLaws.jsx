"use client";
import {
  Container,
  Grid,
  AccordionSummary,
  AccordionDetails,
  Accordion,
} from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import SvgexpandIcon from "../../assets/images/icons/arrowUp.svg";

import SvgCallendar from "@/assets/images/icons/yellow/yellowCalendar.svg";

export default function EgyptLaborLaws() {
  const [expanded, setExpanded] = useState(1);
  const { t } = useTranslation();
  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
    <div id="labor-tn-laws">
      <Container className="custom-max-width">
        <h2 className="heading-h1">{t("Egypte:EgypteLabor:title")}</h2>

        <Grid className="container" container columnSpacing={3} rowSpacing={2}>
          <Grid item xs={12} sm={12} key={1}>
            <Accordion
              elevation={0}
              expanded={expanded === 1}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(1)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Egypte:EgypteLabor:workingHours:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:workingHours:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:workingHours:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:workingHours:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:workingHours:description2")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={2}>
            <Accordion
              elevation={0}
              expanded={expanded === 2}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(2)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Egypte:EgypteLabor:employmentContracts:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:employmentContracts:data")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:employmentContracts:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:employmentContracts:data1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:employmentContracts:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:employmentContracts:description")}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={3}>
            <Accordion
              elevation={0}
              expanded={expanded === 3}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(3)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Egypte:EgypteLabor:termination:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:termination:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:termination:description1")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:termination:titleTermination")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:termination:descriptionTermination")}
                  </p>

                  <ul className="service-description paragraph">
                    <li>{t("Egypte:EgypteLabor:termination:data1")}</li>
                    <li>{t("Egypte:EgypteLabor:termination:data2")}</li>
                    <li>{t("Egypte:EgypteLabor:termination:data3")}</li>
                    <li>{t("Egypte:EgypteLabor:termination:data4")}</li>
                  </ul>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:termination:title2")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:termination:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:termination:title3")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:termination:description3")}{" "}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={4}>
            <Accordion
              elevation={0}
              expanded={expanded === 4}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(4)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Egypte:EgypteLabor:payroll:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:payroll:title1")}
                  </p>
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:payroll:description")}
                  </p>
                </div>
                <div className="payroll-tn">
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("Egypte:EgypteLabor:payroll:fiscalYear:title")}
                    </p>
                    <p className="date">
                      {t("Egypte:EgypteLabor:payroll:fiscalYear:date1")}
                      <br />
                      {t("Egypte:EgypteLabor:payroll:fiscalYear:date2")}
                    </p>
                    <p className="paragraph">
                      {t("Egypte:EgypteLabor:payroll:fiscalYear:description")}
                    </p>
                  </div>
                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("Egypte:EgypteLabor:payroll:payrollCycle:title")}
                    </p>
                    <p className="date">
                      {t("Egypte:EgypteLabor:payroll:payrollCycle:date")}
                    </p>
                    <p className="paragraph">
                      {t("Egypte:EgypteLabor:payroll:payrollCycle:description")}
                    </p>
                  </div>

                  <div className="payroll-tn-item">
                    <p className="title">
                      {t("Egypte:EgypteLabor:payroll:minimumWage:title")}
                    </p>
                    <p className="date">
                      {t("Egypte:EgypteLabor:payroll:minimumWage:wage")}
                      <br />
                      {t("Egypte:EgypteLabor:payroll:minimumWage:date")}​
                    </p>
                    <p className="paragraph">
                      {t("Egypte:EgypteLabor:payroll:minimumWage:description")}
                    </p>
                  </div>                
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={5}>
            <Accordion
              elevation={0}
              expanded={expanded === 5}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(5)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Egypte:EgypteLabor:leaveEntitlements:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:leaveEntitlements:description")}
                  </p>
                  <div className="holidays-dates">
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS1:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS1:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS2:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS2:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS3:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS3:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS4:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS4:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS5:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS5:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS6:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS6:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS7:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS7:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS8:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS8:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS9:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS9:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS10:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS10:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS11:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS11:title"
                        )}
                      </p>
                    </div>
                    <div className="item">
                      <p className="title">
                        <SvgCallendar />{" "}
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS12:date"
                        )}
                      </p>
                      <p className="paragraph">
                        {t(
                          "Egypte:EgypteLabor:leaveEntitlements:leaves:dataS12:title"
                        )}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:annualLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:annualLeave:description1"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:maternityLeave:title"
                    )}
                  </p>
                  <p className="service-description paragraph">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:maternityLeave:description1"
                    )}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:sickLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:sickLeave:description"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:casualLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:casualLeave:description"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:PilgrimageLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:PilgrimageLeave:description"
                    )}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:parentalLeave:title"
                    )}
                  </p>

                  <p className="service-description paragraph">
                    {t(
                      "Egypte:EgypteLabor:leaveEntitlements:leaves:parentalLeave:description"
                    )}
                  </p>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
          <Grid item xs={12} sm={12} key={6}>
            <Accordion
              elevation={0}
              expanded={expanded === 6}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(6)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Egypte:EgypteLabor:tax:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:tax:description")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:tax:title1")}
                  </p>

                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:tax:description1")}
                  </p>

                  <ul className="service-description paragraph">
                    <li> {t("Egypte:EgypteLabor:tax:data1")}</li>
                    <li> {t("Egypte:EgypteLabor:tax:data2")}</li>
                    <li> {t("Egypte:EgypteLabor:tax:data3")}</li>
                    <li> {t("Egypte:EgypteLabor:tax:data4")}</li>
                  </ul>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {" "}
                    {t("Egypte:EgypteLabor:tax:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:tax:description2")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("Egypte:EgypteLabor:tax:dataS1")}</li>
                    <li>{t("Egypte:EgypteLabor:tax:dataS2")}</li>
                    <li>{t("Egypte:EgypteLabor:tax:dataS3")}</li>
                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>

          <Grid item xs={12} sm={12} key={7}>
            <Accordion
              elevation={0}
              expanded={expanded === 7}
              className="services-accordion"
              disableGutters={true}
              onChange={handleChange(7)}
            >
              <AccordionSummary
                aria-controls="panel1bh-content"
                id="panel1bh-header"
                className="services-accordion-header"
                expandIcon={<SvgexpandIcon />}
              >
                <h3 className="service-title">
                  {t("Egypte:EgypteLabor:visa:title")}
                </h3>
              </AccordionSummary>
              <AccordionDetails elevation={0}>
                <div className="item">
                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:visa:description1")}
                    <br />
                    {t("Egypte:EgypteLabor:visa:description2")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:visa:title1")}
                  </p>
                </div>

                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:visa:title2")}
                  </p>

                  <p className="service-description paragraph">
                    {t("Egypte:EgypteLabor:visa:description3")}
                  </p>
                </div>
                <div className="item">
                  <p className="service-sub-title">
                    {t("Egypte:EgypteLabor:visa:title3")}
                  </p>
                  <ul className="service-description paragraph">
                    <li>{t("Egypte:EgypteLabor:visa:data1")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data2")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data3")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data4")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data5")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data6")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data7")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data8")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data9")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data10")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data11")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data12")}</li>
                    <li>{t("Egypte:EgypteLabor:visa:data13")}</li>

                  </ul>
                </div>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Container>
    </div>
  );
}
