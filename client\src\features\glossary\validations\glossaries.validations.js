"use client";

import * as Yup from "yup";

export const languageShape = (t) => ({
  word: Yup.string().required(t("validations:emptyField")),
  letter: Yup.string().required(t("validations:emptyField")),
  visibility: Yup.string().required(t("validations:emptyField")),
  url: Yup.string().required(t("validations:emptyField")),
  content: Yup.string().required(t("validations:emptyField")),
  metaTitle: Yup.string().required(t("validations:emptyField")),
  metaDescription: Yup.string().required(t("validations:emptyField")),
  createdAt: Yup.date()
    .typeError(t("validations:invalidDate", { field: "publish date" }))
    .required(t("validations:emptyField", { field: "publish date" })),
});

export const validationGlossarySchema = (t, selectedLanguages) =>
  Yup.object().shape({
    robotsMeta: Yup.string().required(t("validations:emptyField")),
    en: selectedLanguages.en
      ? Yup.object().shape(languageShape(t))
      : Yup.mixed().notRequired(),
    fr: selectedLanguages.fr
      ? Yup.object().shape(languageShape(t))
      : Yup.mixed().notRequired(),
  });
