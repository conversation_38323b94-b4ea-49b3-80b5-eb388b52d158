"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_features_glossary_component_OptimizedPentabellCompanySection_jsx"],{

/***/ "(app-pages-browser)/./src/features/glossary/component/OptimizedPentabellCompanySection.jsx":
/*!******************************************************************************!*\
  !*** ./src/features/glossary/component/OptimizedPentabellCompanySection.jsx ***!
  \******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n\nvar _s = $RefreshSig$();\n\n\n// Lazy load heavy components\nconst PentabellOfficesIcon = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_assets_images_website_PentabellOfficesIcon_svg\").then(__webpack_require__.bind(__webpack_require__, /*! @/assets/images/website/PentabellOfficesIcon.svg */ \"(app-pages-browser)/./src/assets/images/website/PentabellOfficesIcon.svg\")), {\n    loadableGenerated: {\n        modules: [\n            \"features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx -> \" + \"@/assets/images/website/PentabellOfficesIcon.svg\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-32 h-32 bg-gray-200 rounded\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx\",\n            lineNumber: 6,\n            columnNumber: 18\n        }, undefined),\n    ssr: false\n});\n_c = PentabellOfficesIcon;\nconst OptimizedPentabellCompanySection = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c1 = _s(function OptimizedPentabellCompanySection(param) {\n    let { language } = param;\n    _s();\n    const handleContactUsClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const url = language === \"en\" ? \"https://www.pentabell.com/contact/\" : \"https://www.pentabell.com/\".concat(language, \"/contact/\");\n        // Use window.open with noopener for security\n        window.open(url, \"_blank\", \"noopener,noreferrer\");\n    }, [\n        language\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"pentabell-company bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 my-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col lg:flex-row items-center gap-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"content flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"title text-2xl font-bold text-gray-900 mb-4\",\n                            children: \"Pentabell Company\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"description text-gray-700 leading-relaxed mb-6\",\n                            children: language === \"en\" ? \"Pentabell is a leading international recruitment and HR services company, helping businesses find the right talent and providing comprehensive workforce solutions across multiple industries and regions.\" : \"Pentabell est une entreprise leader en recrutement international et services RH, aidant les entreprises \\xe0 trouver les bons talents et fournissant des solutions compl\\xe8tes de main-d'œuvre dans plusieurs secteurs et r\\xe9gions.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"btn btn-filled-yellow bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold py-3 px-6 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-300\",\n                            onClick: handleContactUsClick,\n                            \"aria-label\": language === \"en\" ? \"Contact Pentabell\" : \"Contacter Pentabell\",\n                            children: language === \"en\" ? \"Contact Us\" : \"Nous Contacter\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"pentabell-offices-icon flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PentabellOfficesIcon, {\n                        className: \"w-32 h-32\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx\",\n                        lineNumber: 44,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\features\\\\glossary\\\\component\\\\OptimizedPentabellCompanySection.jsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}, \"2fYvR9+vrK1rBU6DrBnDaQmn9QE=\")), \"2fYvR9+vrK1rBU6DrBnDaQmn9QE=\");\n_c2 = OptimizedPentabellCompanySection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (OptimizedPentabellCompanySection);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"PentabellOfficesIcon\");\n$RefreshReg$(_c1, \"OptimizedPentabellCompanySection$memo\");\n$RefreshReg$(_c2, \"OptimizedPentabellCompanySection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/glossary/component/OptimizedPentabellCompanySection.jsx\n"));

/***/ })

}]);