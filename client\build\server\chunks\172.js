exports.id=172,exports.ids=[172],exports.modules={21656:(e,t,a)=>{"use strict";a.d(t,{Z:()=>b});var s=a(17577),r=a(41135),n=a(82483),c=a(97898),o=a(88634),i=a(12809),u=a(97631),l=a(35627),d=a(4569),y=a(61213),m=a(64416),g=a(10326);let p=(0,d.Z)(),h=(0,i.Z)("div",{name:"<PERSON>iStack",slot:"Root",overridesResolver:(e,t)=>t.root});function $(e){return(0,u.Z)({props:e,name:"<PERSON><PERSON>Stack",defaultTheme:p})}let w=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],f=({ownerState:e,theme:t})=>{let a={display:"flex",flexDirection:"column",...(0,y.k9)({theme:t},(0,y.P$)({values:e.direction,breakpoints:t.breakpoints.values}),e=>({flexDirection:e}))};if(e.spacing){let s=(0,m.hB)(t),r=Object.keys(t.breakpoints.values).reduce((t,a)=>(("object"==typeof e.spacing&&null!=e.spacing[a]||"object"==typeof e.direction&&null!=e.direction[a])&&(t[a]=!0),t),{}),c=(0,y.P$)({values:e.direction,base:r}),o=(0,y.P$)({values:e.spacing,base:r});"object"==typeof c&&Object.keys(c).forEach((e,t,a)=>{if(!c[e]){let s=t>0?c[a[t-1]]:"column";c[e]=s}}),a=(0,n.Z)(a,(0,y.k9)({theme:t},o,(t,a)=>e.useFlexGap?{gap:(0,m.NA)(s,t)}:{"& > :not(style):not(style)":{margin:0},"& > :not(style) ~ :not(style)":{[`margin${w(a?c[a]:e.direction)}`]:(0,m.NA)(s,t)}}))}return(0,y.dt)(t.breakpoints,a)};var A=a(91703),v=a(2791);let b=function(e={}){let{createStyledComponent:t=h,useThemeProps:a=$,componentName:n="MuiStack"}=e,i=()=>(0,o.Z)({root:["root"]},e=>(0,c.ZP)(n,e),{}),u=t(f);return s.forwardRef(function(e,t){let n=a(e),{component:c="div",direction:o="column",spacing:d=0,divider:y,children:m,className:p,useFlexGap:h=!1,...$}=(0,l.Z)(n),w=i();return(0,g.jsx)(u,{as:c,ownerState:{direction:o,spacing:d,useFlexGap:h},ref:t,className:(0,r.Z)(w.root,p),...$,children:y?function(e,t){let a=s.Children.toArray(e).filter(Boolean);return a.reduce((e,r,n)=>(e.push(r),n<a.length-1&&e.push(s.cloneElement(t,{key:`separator-${n}`})),e),[])}(m,y):m})})}({createStyledComponent:(0,A.ZP)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root}),useThemeProps:e=>(0,v.i)({props:e,name:"MuiStack"})})},64504:(e,t,a)=>{"use strict";a.d(t,{QZ:()=>b,qA:()=>P,$F:()=>x,He:()=>Z,wv:()=>R,bh:()=>M,b$:()=>S,KK:()=>N,Py:()=>E,hb:()=>F,Yg:()=>I,mg:()=>k,P0:()=>Q,Cb:()=>X,el:()=>C,IX:()=>Y});var s=a(2994),r=a(50967),n=a(70580),c=a(31190);let o=e=>(e.t,new Promise(async(t,a)=>{n.yX.post(r.Y.articles,e.data).then(e=>{c.Am.success("Article added successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&a(e)})})),i=e=>(e.t,new Promise(async(t,a)=>{n.yX.post(`${r.Y.articles}/auto`,e.data).then(e=>{e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&a(e)})})),u=({data:e,id:t})=>new Promise(async(a,s)=>{n.yX.put(`${r.Y.articles}/${t}/auto`,e).then(e=>{e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),l=({data:e,id:t})=>new Promise(async(a,s)=>{n.yX.put(`${r.Y.articles}/${t}`,e).then(e=>{c.Am.success("article Commun fields updated successfully"),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),d=e=>new Promise(async(t,a)=>{try{let a={};a=await n.yX.get(`${r.Y.articles}/${e.language}/blog/${e.urlArticle}`),t(a.data)}catch(e){e&&e.response&&e.response.data&&e.response.status,a(e)}}),y=({data:e,language:t,id:a})=>new Promise(async(s,o)=>{n.yX.post(`${r.Y.articles}/${t}/${a}`,e).then(e=>{"en"===t&&c.Am.success("Article english updated successfully"),"fr"===t&&c.Am.success("Article french updated successfully"),e?.data&&s(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})}),m=(e,t,a)=>new Promise(async(s,o)=>{try{let o=await n.xk.put(`${r.Y.articles}/${e}/${t}/desarchiver`,{archive:a});o?.data&&(c.Am.success(`Article ${a?"archived":"desarchived"} successfully`),s(o.data))}catch(e){c.Am.error(`Failed to ${a?"archive":"desarchive"} the article.`),o(e)}}),g=e=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.categories}/${e}/all`);t(a.data)}catch(e){a(e)}}),p=e=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.articles}`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isThreeLastArticles:e.isThreeLastArticles,isArchived:e.isArchived,categoryName:e.categoryName}});t(a.data)}catch(e){a(e)}}),h=e=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.articles}/dashboard`,{params:{paginated:e.paginated,language:e.language,pageSize:e.pageSize,pageNumber:e.pageNumber,sortOrder:e.sortOrder,searchQuery:e.searchQuery,visibility:e.visibility,createdAt:e.createdAt,publishDate:e.publishDate,isArchived:e.isArchived,categoryName:e.categoryName}});t(a.data)}catch(e){a(e)}}),$=e=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.articles}/${e.language}/listarticle`);t(a.data)}catch(e){a(e)}}),w=({articleId:e,pageNumber:t,pageSize:a,sortOrder:s,name:c,approved:o,createdAt:i,paginated:u})=>new Promise(async(l,d)=>{try{let d=`${r.v}/comments/${e}?pageSize=${encodeURIComponent(a)}&pageNumber=${encodeURIComponent(t)}&sortOrder=${encodeURIComponent(s)}&paginated=${encodeURIComponent(u)}`;c&&(d+=`&name=${encodeURIComponent(c)}`),o&&(d+=`&approved=${encodeURIComponent(o)}`),i&&(d+=`&createdAt=${encodeURIComponent(new Date(i).toISOString())}`);let y=await n.yX.get(d);l(y.data)}catch(e){d(e)}}),f=(e,t)=>new Promise(async(a,s)=>{try{let s=await n.yX.get(`${r.Y.articles}/${t}/${e}`);a(s.data)}catch(e){s(e)}}),A=e=>new Promise(async(t,a)=>{try{let a=await n.xk.get(`${r.Y.comments}/detail/${e}`);t(a.data)}catch(e){a(e)}}),v=(e,t)=>new Promise(async(t,a)=>{try{let a=await n.yX.get(`${r.Y.articles}/${e}`);t(a.data)}catch(e){a(e)}});a(97980);let b=()=>(0,s.useMutation)({mutationFn:e=>o(e),onError:e=>{e.message=""}}),P=()=>(0,s.useMutation)({mutationFn:e=>i(e),onError:e=>{e.message=""}}),C=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>u(e,t),onError:e=>{e.message=""}})),Q=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t,a)=>y(e,t,a),onError:e=>{e.message=""}})),Y=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:({language:e,id:t,archive:a})=>m(e,t,a),onError:e=>{console.error("Error during mutation",e),e.message=""}})),X=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>l(e,t),onError:e=>{e.message=""}})),E=e=>(0,s.useQuery)(["category",e],async()=>await g(e)),k=e=>(0,s.useQuery)(["service",e],async()=>await g(e)),M=e=>(0,s.useQuery)("article",async()=>await p(e)),S=e=>(0,s.useQuery)(`articles${e.language}`,async()=>await h(e)),N=e=>(0,s.useQuery)(`articlestitles${e.language}`,async()=>await $(e)),F=(e,t={})=>(0,s.useQuery)("comment",async()=>await w(e),{...t}),R=(e,t={})=>(0,s.useQuery)(["article",e],async()=>{try{return await d(e)}catch(t){throw t.response&&404===t.response.status&&("en"===e.language?window.location.href="/blog/":window.location.href="/fr/blog/"),t}},{onError:e=>{console.error("Error fetching article:",e.message)},...t}),x=(e,t)=>(0,s.useQuery)(["article",e,t],async()=>await f(e,t)),Z=e=>(0,s.useQuery)(["articleall",e],async()=>await v(e)),I=e=>(0,s.useQuery)(["comment",e],async()=>await A(e))},41611:(e,t,a)=>{"use strict";a.d(t,{z9:()=>h,HL:()=>y,$u:()=>p,x2:()=>m,Ny:()=>g});var s=a(2994),r=a(31190),n=a(50967),c=a(70580);let o=e=>{let t=e.t;return new Promise(async(a,s)=>{c.yX.post(n.Y.category,e.data).then(e=>{r.Am.success(t("messages:categoryAdded")),e?.data&&a(e.data)}).catch(e=>{e&&e.response&&e.response.data&&(e?.response?.data?.status===409||e?.status===409)&&r.Am.warning(t("messages:categoryNameExists")),e&&s(e)})})},i=e=>new Promise(async(t,a)=>{try{let a=await c.yX.get(`${n.Y.categories}/${e}`);t(a.data)}catch(e){a(e)}}),u=({data:e,language:t,id:a})=>new Promise(async(s,o)=>{c.yX.post(`${n.Y.category}/${t}/${a}`,e).then(e=>{"en"===t&&r.Am.success("Category english updated successfully"),"fr"===t&&r.Am.success("Category french updated successfully"),e?.data&&s(e.data)}).catch(e=>{e?.response?.data?.status===500||e?.status===500?r.Am.error("Internal Server Error"):r.Am.error(e.response.data.message)})}),l=({language:e,id:t})=>new Promise(async(a,s)=>{c.yX.delete(`${n.Y.category}/${e}/${t}`).then(t=>{"en"===e&&r.Am.success("Category english deleted successfully"),"fr"===e&&r.Am.success("Category french deleted successfully"),t?.data&&a(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),d=({language:e,idCategory:t,idNewCategory:a})=>new Promise(async(s,o)=>{c.yX.delete(`${n.Y.articles}${n.Y.category}/${t}/${a}`).then(t=>{"en"===e&&r.Am.success("Category english deleted successfully"),"fr"===e&&r.Am.success("Category french deleted successfully"),t?.data&&s(t.data)}).catch(e=>{e&&e.response&&e.response.data,e&&o(e)})}),y=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:e=>o(e),onError:e=>{e.message=""}})),m=e=>(0,s.useQuery)(["categoriesData",e],async()=>await i(e)),g=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t,a)=>u(e,t,a),onError:e=>{e.message=""}})),p=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t)=>l(e,t),onError:e=>{e.message=""}})),h=()=>((0,s.useQueryClient)(),(0,s.useMutation)({mutationFn:(e,t,a)=>d(e,t,a),onError:e=>{e.message=""}}))},86648:()=>{}};