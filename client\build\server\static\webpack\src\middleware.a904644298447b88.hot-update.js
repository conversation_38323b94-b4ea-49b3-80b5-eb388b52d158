"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("src/middleware",{

/***/ "(middleware)/./src/middleware.js":
/*!***************************!*\
  !*** ./src/middleware.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-i18n-router */ \"(middleware)/./node_modules/next-i18n-router/dist/index.js\");\n/* harmony import */ var next_i18n_router__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_i18n_router__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jose__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! jose */ \"(middleware)/./node_modules/jose/dist/browser/jwt/verify.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! cookie */ \"(middleware)/./node_modules/cookie/index.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../i18nConfig */ \"(middleware)/./i18nConfig.js\");\n/* harmony import */ var _i18nConfig__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_i18nConfig__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _utils_functions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/functions */ \"(middleware)/./src/utils/functions.js\");\n/* harmony import */ var _config_translations__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config/translations */ \"(middleware)/./src/config/translations.js\");\n/* harmony import */ var _config_allowedParams__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./config/allowedParams */ \"(middleware)/./src/config/allowedParams.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\n\n\nconst verifyToken = async (token)=>{\n    try {\n        const secret = new TextEncoder().encode(process.env.NEXT_JWT_SECRET);\n        const { payload } = await (0,jose__WEBPACK_IMPORTED_MODULE_9__.jwtVerify)(token, secret);\n        return payload;\n    } catch (error) {\n        return null;\n    }\n};\nasync function middleware(req) {\n    const url = req.nextUrl.clone();\n    const { defaultLocale } = (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default());\n    const { pathname } = req.nextUrl;\n    // authentication and authorisation\n    const cookies = cookie__WEBPACK_IMPORTED_MODULE_2__.parse(req.headers.get(\"cookie\") || \"\");\n    const { accessToken, refreshToken } = cookies;\n    if (!(accessToken && refreshToken) && (pathname.includes(`dashboard`) || pathname.includes(`backoffice`))) {\n        url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    const user = await verifyToken(refreshToken);\n    if (pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.logout.route}/`) {\n        return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n    }\n    if (user) {\n        const menuList = (0,_utils_functions__WEBPACK_IMPORTED_MODULE_5__.getRoutesListByRole)(user);\n        const checkRole = !menuList?.some((item)=>pathname.includes(item)) && (pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}`) || pathname?.includes(`/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}`));\n        if (checkRole || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/fr/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.register.route}/` || pathname === `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.authRoutes.login.route}/`) {\n            if (user.roles?.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.home.route}/`;\n            if (user.roles?.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlBackoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.adminRoutes.blogs.route}/`;\n            if (user?.roles?.includes(_utils_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) url.pathname = `/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.baseUrlFrontoffice.baseURL.route}/${_helpers_routesList__WEBPACK_IMPORTED_MODULE_8__.candidateRoutes.myApplications.route}`;\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n        }\n    }\n    // remove any unallowed params\n    for (const param of url.searchParams.keys()){\n        if (!_config_allowedParams__WEBPACK_IMPORTED_MODULE_7__.allowedParams.has(param)) {\n            url.searchParams.delete(param);\n        }\n    }\n    if (url.searchParams.toString() !== req.nextUrl.searchParams.toString()) {\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(url);\n    }\n    // redirection paths\n    const frPath = _config_translations__WEBPACK_IMPORTED_MODULE_6__[\"default\"][req.nextUrl.pathname];\n    if (frPath) return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL(frPath, req.url));\n    // translate links\n    if (!pathname.startsWith(\"/fr\") && !pathname.startsWith(`/${defaultLocale}`)) {\n        url.pathname = `/en${pathname}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.rewrite(url);\n    }\n    return (0,next_i18n_router__WEBPACK_IMPORTED_MODULE_0__.i18nRouter)(req, (_i18nConfig__WEBPACK_IMPORTED_MODULE_3___default()));\n}\nconst config = {\n    matcher: \"/((?!api|static|.*\\\\..*|_next).*)\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.js\n");

/***/ }),

/***/ "(middleware)/./src/utils/constants.js":
/*!********************************!*\
  !*** ./src/utils/constants.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContractType: () => (/* binding */ ContractType),\n/* harmony export */   Countries: () => (/* binding */ Countries),\n/* harmony export */   Frequence: () => (/* binding */ Frequence),\n/* harmony export */   Gender: () => (/* binding */ Gender),\n/* harmony export */   Industry: () => (/* binding */ Industry),\n/* harmony export */   IndustryCandidat: () => (/* binding */ IndustryCandidat),\n/* harmony export */   LabelContactFields: () => (/* binding */ LabelContactFields),\n/* harmony export */   Nationalities: () => (/* binding */ Nationalities),\n/* harmony export */   OpportunityType: () => (/* binding */ OpportunityType),\n/* harmony export */   RobotsMeta: () => (/* binding */ RobotsMeta),\n/* harmony export */   Role: () => (/* binding */ Role),\n/* harmony export */   Roles: () => (/* binding */ Roles),\n/* harmony export */   Status: () => (/* binding */ Status),\n/* harmony export */   TypeContactLabels: () => (/* binding */ TypeContactLabels),\n/* harmony export */   TypeContacts: () => (/* binding */ TypeContacts),\n/* harmony export */   Visibility: () => (/* binding */ Visibility),\n/* harmony export */   VisibilityEnum: () => (/* binding */ VisibilityEnum),\n/* harmony export */   cible: () => (/* binding */ cible),\n/* harmony export */   contactData: () => (/* binding */ contactData),\n/* harmony export */   coporateProfileTestimonials: () => (/* binding */ coporateProfileTestimonials),\n/* harmony export */   defaultFonts: () => (/* binding */ defaultFonts),\n/* harmony export */   feedbacks: () => (/* binding */ feedbacks),\n/* harmony export */   skills: () => (/* binding */ skills),\n/* harmony export */   sortedFontOptions: () => (/* binding */ sortedFontOptions)\n/* harmony export */ });\nconst Countries = [\n    \"Afghanistan\",\n    \"\\xc5land Islands\",\n    \"Albania\",\n    \"Algeria\",\n    \"American Samoa\",\n    \"AndorrA\",\n    \"Angola\",\n    \"Anguilla\",\n    \"Antarctica\",\n    \"Antigua and Barbuda\",\n    \"Argentina\",\n    \"Armenia\",\n    \"Aruba\",\n    \"Australia\",\n    \"Austria\",\n    \"Azerbaijan\",\n    \"Bahamas\",\n    \"Bahrain\",\n    \"Bangladesh\",\n    \"Barbados\",\n    \"Belarus\",\n    \"Belgium\",\n    \"Belize\",\n    \"Benin\",\n    \"Bermuda\",\n    \"Bhutan\",\n    \"Bolivia\",\n    \"Bosnia and Herzegovina\",\n    \"Botswana\",\n    \"Bouvet Island\",\n    \"Brazil\",\n    \"British Indian Ocean Territory\",\n    \"Brunei Darussalam\",\n    \"Bulgaria\",\n    \"Burkina Faso\",\n    \"Burundi\",\n    \"Cambodia\",\n    \"Cameroon\",\n    \"Canada\",\n    \"Cape Verde\",\n    \"Cayman Islands\",\n    \"Central African Republic\",\n    \"Chad\",\n    \"Chile\",\n    \"China\",\n    \"Christmas Island\",\n    \"Cocos (Keeling) Islands\",\n    \"Colombia\",\n    \"Comoros\",\n    \"Congo\",\n    \"Cook Islands\",\n    \"Costa Rica\",\n    \"Cote D'Ivoire\",\n    \"Croatia\",\n    \"Cuba\",\n    \"Cyprus\",\n    \"Czech Republic\",\n    \"Denmark\",\n    \"Democratic Republic of the Congo\",\n    \"Djibouti\",\n    \"Dominica\",\n    \"Dominican Republic\",\n    \"Ecuador\",\n    \"Egypt\",\n    \"El Salvador\",\n    \"Equatorial Guinea\",\n    \"Eritrea\",\n    \"Estonia\",\n    \"Ethiopia\",\n    \"Falkland Islands (Malvinas)\",\n    \"Faroe Islands\",\n    \"Fiji\",\n    \"Finland\",\n    \"France\",\n    \"French Guiana\",\n    \"French Polynesia\",\n    \"French Southern Territories\",\n    \"Gabon\",\n    \"Gambia\",\n    \"Georgia\",\n    \"Germany\",\n    \"Ghana\",\n    \"Gibraltar\",\n    \"Greece\",\n    \"Greenland\",\n    \"Grenada\",\n    \"Guadeloupe\",\n    \"Guam\",\n    \"Guatemala\",\n    \"Guernsey\",\n    \"Guinea\",\n    \"Guinea-Bissau\",\n    \"Guyana\",\n    \"Haiti\",\n    \"Heard Island and Mcdonald Islands\",\n    \"Holy See (Vatican City State)\",\n    \"Honduras\",\n    \"Hong Kong\",\n    \"Hungary\",\n    \"Iceland\",\n    \"India\",\n    \"Indonesia\",\n    \"Iran, Islamic Republic Of\",\n    \"Iraq\",\n    \"Ireland\",\n    \"Isle of Man\",\n    \"Italy\",\n    \"Jamaica\",\n    \"Japan\",\n    \"Jersey\",\n    \"Jordan\",\n    \"Kazakhstan\",\n    \"Kenya\",\n    \"Kiribati\",\n    \"Korea, Democratic People'S Republic of\",\n    \"Korea, Republic of\",\n    \"Kuwait\",\n    \"Kyrgyzstan\",\n    \"Lao People'S Democratic Republic\",\n    \"Latvia\",\n    \"Lebanon\",\n    \"Lesotho\",\n    \"Liberia\",\n    \"Libya\",\n    \"Liechtenstein\",\n    \"Lithuania\",\n    \"Luxembourg\",\n    \"Macao\",\n    \"Macedonia, The Former Yugoslav Republic of\",\n    \"Madagascar\",\n    \"Malawi\",\n    \"Malaysia\",\n    \"Maldives\",\n    \"Mali\",\n    \"Malta\",\n    \"Marshall Islands\",\n    \"Martinique\",\n    \"Mauritania\",\n    \"Mauritius\",\n    \"Mayotte\",\n    \"Mexico\",\n    \"Micronesia, Federated States of\",\n    \"Moldova, Republic of\",\n    \"Monaco\",\n    \"Mongolia\",\n    \"Montserrat\",\n    \"Morocco\",\n    \"Mozambique\",\n    \"Myanmar\",\n    \"Namibia\",\n    \"Nauru\",\n    \"Nepal\",\n    \"Netherlands\",\n    \"Netherlands Antilles\",\n    \"New Caledonia\",\n    \"New Zealand\",\n    \"Nicaragua\",\n    \"Niger\",\n    \"Nigeria\",\n    \"Niue\",\n    \"Norfolk Island\",\n    \"Northern Mariana Islands\",\n    \"Norway\",\n    \"Oman\",\n    \"Pakistan\",\n    \"Palau\",\n    \"Palestine\",\n    \"Panama\",\n    \"Papua New Guinea\",\n    \"Paraguay\",\n    \"Peru\",\n    \"Philippines\",\n    \"Pitcairn\",\n    \"Poland\",\n    \"Portugal\",\n    \"Puerto Rico\",\n    \"Qatar\",\n    \"Reunion\",\n    \"Romania\",\n    \"Russian Federation\",\n    \"RWANDA\",\n    \"Saint Helena\",\n    \"Saint Kitts and Nevis\",\n    \"Saint Lucia\",\n    \"Saint Pierre and Miquelon\",\n    \"Saint Vincent and the Grenadines\",\n    \"Samoa\",\n    \"San Marino\",\n    \"Sao Tome and Principe\",\n    \"Saudi Arabia\",\n    \"Senegal\",\n    \"Serbia and Montenegro\",\n    \"Seychelles\",\n    \"Sierra Leone\",\n    \"Singapore\",\n    \"Slovakia\",\n    \"Slovenia\",\n    \"Solomon Islands\",\n    \"Somalia\",\n    \"South Africa\",\n    \"South Georgia and the South Sandwich Islands\",\n    \"Spain\",\n    \"Sri Lanka\",\n    \"Sudan\",\n    \"Suriname\",\n    \"Svalbard and Jan Mayen\",\n    \"Swaziland\",\n    \"Sweden\",\n    \"Switzerland\",\n    \"Syrian Arab Republic\",\n    \"Taiwan, Province of China\",\n    \"Tajikistan\",\n    \"Tanzania, United Republic of\",\n    \"Thailand\",\n    \"Timor-Leste\",\n    \"Togo\",\n    \"Tokelau\",\n    \"Tonga\",\n    \"Trinidad and Tobago\",\n    \"Tunisia\",\n    \"Turkey\",\n    \"Turkmenistan\",\n    \"Turks and Caicos Islands\",\n    \"Tuvalu\",\n    \"Uganda\",\n    \"Ukraine\",\n    \"United Arab Emirates\",\n    \"United Kingdom\",\n    \"United States\",\n    \"United States Minor Outlying Islands\",\n    \"Uruguay\",\n    \"Uzbekistan\",\n    \"Vanuatu\",\n    \"Venezuela\",\n    \"Viet Nam\",\n    \"Virgin Islands, British\",\n    \"Virgin Islands, U.S.\",\n    \"Wallis and Futuna\",\n    \"Western Sahara\",\n    \"Yemen\",\n    \"Zambia\",\n    \"Zimbabwe\"\n];\nconst ContractType = [\n    \"CDD\",\n    \"CDIC\",\n    \"Freelance\"\n];\nconst Nationalities = [\n    \"American\",\n    \"British\",\n    \"Canadian\",\n    \"French\",\n    \"German\",\n    \"Italian\",\n    \"Japanese\",\n    \"Chinese\",\n    \"Indian\",\n    \"Russian\",\n    \"Australian\",\n    \"Brazilian\",\n    \"Mexican\",\n    \"Spanish\",\n    \"South Korean\",\n    \"Dutch\",\n    \"Swedish\",\n    \"Tunisian\",\n    \"Norwegian\",\n    \"Swiss\",\n    \"Belgian\"\n];\nconst Gender = [\n    \"Male\",\n    \"Female\",\n    \"All\"\n];\nconst Frequence = [\n    \"monthly\",\n    \"weekly\"\n];\nconst Visibility = [\n    \"Public\",\n    \"Private\",\n    \"Draft\"\n];\nconst VisibilityEnum = {\n    Public: \"Public\",\n    Private: \"Private\",\n    Draft: \"Draft\"\n};\n// export const OpportunityTypeLabel = {\n//   CONFIDENTIAL: \"Confidential\",\n//   DIRECT_HIRE: \"Direct Hire\",\n//   TENDER: \"Tender\",\n//   CAPABILITY: \"Capability\",\n//   PAYROLL: \"Payroll\",\n//   INTERNE: \"Intern\",\n//   RECRUTEMENT: \"Recrutement\",\n//   CONSULTING: \"Consulting\",\n//   PORTAGE: \"Portage\",\n//   NOT_SPECIFIED: \"Not specified\",\n// };\nconst OpportunityType = [\n    \"Confidential\",\n    \"Direct Hire\",\n    \"Tender\",\n    \"Capability\",\n    \"Payroll\",\n    \"In House\",\n    \"Recrutement\",\n    \"Consulting\",\n    \"Portage\",\n    \"Not specified\"\n];\n// export const ContractType = [\n// \"Permanent contract\",\n// \"Temporary\",\n// \"Freelance\",\n// \"Work study\",\n// \"Internship\",\n// \"Part-time\",\n// \"Graduate program\",\n// \"Volunteer work\",\n// \"Other\"\n// ]\nconst RobotsMeta = [\n    \"index\",\n    \"noindex\"\n];\nconst Roles = [\n    \"Candidate\",\n    \"Editor\",\n    \"Admin\"\n];\nconst Role = {\n    CANDIDATE: \"Candidate\",\n    EDITOR: \"Editor\",\n    ADMIN: \"Admin\"\n};\nconst Status = [\n    \"Pending\",\n    \"Accepted\",\n    \"Rejected\"\n];\nconst Industry = [\n    \"It & Telecom\",\n    \"Transport\",\n    \"Energies\",\n    \"Banking\",\n    \"Pharmaceutical\",\n    \"Other\"\n];\nconst IndustryCandidat = [\n    \"It & Telecom\",\n    \"Transport\",\n    \"Oil & gas\",\n    \"Energy\",\n    \"Banking\",\n    \"Pharmaceutical\"\n];\nconst cible = [\n    \"client\",\n    \"consultant\"\n];\nconst skills = [\n    // Compétences pour IT & TELECOM\n    {\n        name: \"D\\xe9veloppement logiciel\",\n        label: \"D\\xe9veloppement logiciel\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Administration syst\\xe8me\",\n        label: \"Administration syst\\xe8me\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"D\\xe9veloppement d'applications mobiles\",\n        label: \"D\\xe9veloppement d'applications mobiles\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Gestion de r\\xe9seau\",\n        label: \"Gestion de r\\xe9seau\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Gestion de projet\",\n        label: \"Gestion de projet\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Analyse de donn\\xe9es\",\n        label: \"Analyse de donn\\xe9es\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Cybers\\xe9curit\\xe9\",\n        label: \"Cybers\\xe9curit\\xe9\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"Cloud computing\",\n        label: \"Cloud computing\",\n        industry: \"IT & TELECOM\"\n    },\n    {\n        value: \"abcdabcd\",\n        label: \"abcdabcd\",\n        industry: \"IT & TELECOM\"\n    },\n    // Compétences pour TRANSPORT\n    {\n        value: \"Transport routier\",\n        label: \"Transport routier\",\n        industry: \"TRANSPORT\"\n    },\n    {\n        value: \"Logistique\",\n        label: \"Logistique\",\n        industry: \"TRANSPORT\"\n    },\n    {\n        value: \"Gestion de flotte\",\n        label: \"Gestion de flotte\",\n        industry: \"TRANSPORT\"\n    },\n    {\n        value: \"Planification des itin\\xe9raires\",\n        label: \"Planification des itin\\xe9raires\",\n        industry: \"TRANSPORT\"\n    },\n    {\n        value: \"Logistique internationale\",\n        label: \"Logistique internationale\",\n        industry: \"TRANSPORT\"\n    },\n    // Compétences pour OIL & GAS\n    {\n        value: \"Forage p\\xe9trolier\",\n        label: \"Forage p\\xe9trolier\",\n        industry: \"OIL & GAS\"\n    },\n    {\n        value: \"Raffinage p\\xe9trolier\",\n        label: \"Raffinage p\\xe9trolier\",\n        industry: \"OIL & GAS\"\n    },\n    {\n        value: \"Exploration g\\xe9ologique\",\n        label: \"Exploration g\\xe9ologique\",\n        industry: \"OIL & GAS\"\n    },\n    {\n        value: \"Ing\\xe9nierie des r\\xe9servoirs\",\n        label: \"Ing\\xe9nierie des r\\xe9servoirs\",\n        industry: \"OIL & GAS\"\n    },\n    {\n        value: \"Gestion de la production\",\n        label: \"Gestion de la production\",\n        industry: \"OIL & GAS\"\n    },\n    // Compétences pour BANKING\n    {\n        value: \"Analyse financi\\xe8re\",\n        label: \"Analyse financi\\xe8re\",\n        industry: \"BANKING\"\n    },\n    {\n        value: \"Gestion des risques financiers\",\n        label: \"Gestion des risques financiers\",\n        industry: \"BANKING\"\n    },\n    {\n        value: \"Gestion des portefeuilles\",\n        label: \"Gestion des portefeuilles\",\n        industry: \"BANKING\"\n    },\n    {\n        value: \"Conformit\\xe9 r\\xe9glementaire\",\n        label: \"Conformit\\xe9 r\\xe9glementaire\",\n        industry: \"BANKING\"\n    },\n    {\n        value: \"Services bancaires en ligne\",\n        label: \"Services bancaires en ligne\",\n        industry: \"BANKING\"\n    }\n];\nconst defaultFonts = [\n    \"Arial\",\n    \"Comic Sans MS\",\n    \"Courier New\",\n    \"Impact\",\n    \"Georgia\",\n    \"Tahoma\",\n    \"Trebuchet MS\",\n    \"Verdana\"\n];\nconst sortedFontOptions = [\n    \"Logical\",\n    \"Salesforce Sans\",\n    \"Garamond\",\n    \"Sans-Serif\",\n    \"Serif\",\n    \"Times New Roman\",\n    \"Helvetica\",\n    ...defaultFonts\n].sort();\nconst TypeContacts = [\n    \"countryContact\",\n    \"joinUs\",\n    \"directHiringService\",\n    \"aiSourcingService\",\n    \"technicalAssistanceService\",\n    \"consultingService\",\n    \"payrollService\",\n    \"mainService\",\n    \"getInTouchContact\",\n    \"getInTouch\"\n];\nconst TypeContactLabels = {\n    countryContact: \"Country Contact\",\n    joinUs: \"Join Us\",\n    directHiringService: \"Direct Hiring Service\",\n    aiSourcingService: \"AI Sourcing Service\",\n    technicalAssistanceService: \"Technical Assistance Service\",\n    consultingService: \"Consulting Service\",\n    payrollService: \"Payroll Service\",\n    mainService: \"Main Service\",\n    getInTouchContact: \"Get in Touch Contact\",\n    getInTouch: \"Get in Touch\"\n};\nconst LabelContactFields = {\n    firstName: \"First Name\",\n    lastName: \"Last Name\",\n    fullName: \"Full Name\",\n    email: \"Email\",\n    phone: \"Phone\",\n    message: \"Message\",\n    type: \"Type\",\n    subject: \"Subject\",\n    youAre: \"You Are\",\n    companyName: \"Company Name\",\n    enquirySelect: \"Enquiry Select\",\n    jobTitle: \"Job Title\",\n    mission: \"Mission\",\n    resume: \"Resume\",\n    howToHelp: \"How To Help\",\n    createdAt: \"Created At\",\n    countryName: \"Country Name\",\n    field: \"Field\"\n};\nconst contactData = (t, locale)=>[\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:france\"),\n            logo: {\n                \"@type\": \"ImageObject\",\n                url: \"https://www.pentabell.com/logos/pentabell-logo.png\"\n            },\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Atlantic Building Montparnasse, Entrance No. 7, 3rd floor\",\n                addressLocality: \"Paris\",\n                postalCode: \"75015\",\n                addressCountry: \"FR\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/recruitment-agency-france/\" : `https://www.pentabell.com/${locale}/recruitment-agency-france/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:switzerland\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Grand-Rue 92\",\n                addressLocality: \"Montreux\",\n                postalCode: \"1820\",\n                addressCountry: \"CH\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/contact/\" : `https://www.pentabell.com/${locale}/contact/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:ksa\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"3530 Umar Ibn Abdul Aziz Br Rd, Az Zahra\",\n                addressLocality: \"Riyadh\",\n                postalCode: \"12815\",\n                addressCountry: \"SA\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/international-hr-services-recruitment-agency-ksa/\" : `https://www.pentabell.com/${locale}/international-hr-services-recruitment-agency-ksa/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:uae\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"HDS Business Center Office 306 JLT\",\n                addressLocality: \"Dubai\",\n                addressCountry: \"AE\"\n            },\n            telephone: \"+971 4 4876 0672\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/recruitment-staffing-agency-dubai/\" : `https://www.pentabell.com/${locale}/recruitment-staffing-agency-dubai/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:qatar\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Level 14, Commercial Bank Plaza, West Bay\",\n                addressLocality: \"Doha\",\n                postalCode: \"27111\",\n                addressCountry: \"QA\"\n            },\n            telephone: \"+974 4452 7957\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/international-hr-services-recruitment-agency-qatar/\" : `https://www.pentabell.com/${locale}/international-hr-services-recruitment-agency-qatar/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:tunisia\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Imm. MADIBA, Rue Khawarizmi\",\n                addressLocality: \"La Goulette\",\n                postalCode: \"2015\",\n                addressCountry: \"TN\"\n            },\n            telephone: [\n                \"+216 31 385 510\"\n            ],\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/hiring-employees-tunisia-guide/\" : `https://www.pentabell.com/${locale}/hiring-employees-tunisia-guide/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:hydra\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Route les oliviers les cretes n\\xb014\",\n                addressLocality: \"Hydra, Alger\",\n                postalCode: \"16035\",\n                addressCountry: \"DZ\"\n            },\n            telephone: [\n                \"+213 23 48 59 10\",\n                \"+213 23 48 51 44\"\n            ],\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/\" : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-algeria/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:hassiMassoud\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Eurojapan Residence Route Nationale N\\xb03 BP 842\",\n                addressLocality: \"Hassi Messaoud\",\n                addressCountry: \"DZ\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-algeria/\" : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-algeria/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:morocco\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Zenith 1, Sidi maarouf, lot CIVIM\",\n                addressLocality: \"Casablanca\",\n                postalCode: \"20270\",\n                addressCountry: \"MA\"\n            },\n            telephone: \"+212 5 22 78 63 66\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/ultimate-guide-to-hiring-employees-in-morocco/\" : `https://www.pentabell.com/${locale}/ultimate-guide-to-hiring-employees-in-morocco/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:egypte\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"8 El Birgas street, Garden City\",\n                addressLocality: \"Cairo\",\n                addressCountry: \"EG\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/guide-to-hiring-employees-in-egypt/\" : `https://www.pentabell.com/${locale}/guide-to-hiring-employees-in-egypt/`\n        },\n        {\n            \"@context\": \"https://schema.org\",\n            \"@type\": \"Organization\",\n            name: t(\"contactUs:bureux:contacts:lybia\"),\n            address: {\n                \"@type\": \"PostalAddress\",\n                streetAddress: \"Al Serraj, AlMawashi Street P.O.Box 3000\",\n                addressLocality: \"Tripoli\",\n                addressCountry: \"LY\"\n            },\n            telephone: \"+33 1 73 07 42 54\",\n            email: \"<EMAIL>\",\n            url: locale === \"en\" ? \"https://www.pentabell.com/guide-to-hiring-employees-in-libya/\" : `https://www.pentabell.com/${locale}/guide-to-hiring-employees-in-libya/`\n        }\n    ];\nconst feedbacks = [\n    {\n        id: 1,\n        description: \"I truly appreciate the opportunities provided and the unwavering trust in my abilities. A big thank you to the entire team for their continued support, especially Ridha R.\",\n        rating: 4,\n        author: \"Nabil.J\",\n        quality: \"IT Coordinator\"\n    },\n    {\n        id: 2,\n        description: \"Pentabell Tunisie team has been helpful and responsive whenever assistance is needed, particularly Mr. Ridha and Mr. Matthew. I appreciate the team’s efforts and look forward to seeing continued growth in the future.\",\n        rating: 4,\n        author: \"Maher.M\",\n        quality: \"A2P Service Delivery Engineer\"\n    },\n    {\n        id: 3,\n        description: \"We are very satisfied with the service provided by Pentabell. The professionalism and efficiency of the Tunisia office staff are commendable, especially the work of Mr. Matthew, Mr. Ridha, and Mr. Eric, who have been our direct contacts.\",\n        rating: 5,\n        author: \"Gabor.M\",\n        quality: \"Company\"\n    },\n    {\n        id: 4,\n        description: \"I truly appreciate the opportunities provided and the unwavering trust in my abilities. A big thank you to the entire team for their continued support, especially Ridha R.\",\n        rating: 4,\n        author: \"Nabil.J\",\n        quality: \"IT Coordinator\"\n    },\n    {\n        id: 5,\n        description: \"Pentabell Tunisie team has been helpful and responsive whenever assistance is needed, particularly Mr. Ridha and Mr. Matthew. I appreciate the team’s efforts and look forward to seeing continued growth in the future.\",\n        rating: 4,\n        author: \"Maher.M\",\n        quality: \"A2P Service Delivery Engineer\"\n    },\n    {\n        id: 6,\n        description: \"We are very satisfied with the service provided by Pentabell. The professionalism and efficiency of the Tunisia office staff are commendable, especially the work of Mr. Matthew, Mr. Ridha, and Mr. Eric, who have been our direct contacts.\",\n        rating: 5,\n        author: \"Gabor.M\",\n        quality: \"Company\"\n    }\n];\nconst coporateProfileTestimonials = [\n    {\n        id: 1,\n        description: \"I am pleased  with PENTABELL the exceptional services they have delivered during our recent collaborations on various projects within the Kingdom of Saudi Arabia (KSA). Throughout our partnership, PENTABELL has consistently demonstrated professionalism, expertise, and a strong commitment to delivering high-quality results.\",\n        author: \"NOKIA KSA\"\n    },\n    {\n        id: 2,\n        description: \"We are very satisfied with the service provided by Pentabell. The professionalism and efficiency of the Tunisia office staff are commendable, especially the work of Mr. Matthew, Mr. Ridha, and Mr. Eric, who have been our direct contacts.\",\n        author: \"Gabor.M, Company\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/utils/constants.js\n");

/***/ }),

/***/ "(middleware)/./src/utils/functions.js":
/*!********************************!*\
  !*** ./src/utils/functions.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirstLetter: () => (/* binding */ capitalizeFirstLetter),\n/* harmony export */   findCountryFlag: () => (/* binding */ findCountryFlag),\n/* harmony export */   findCountryLabel: () => (/* binding */ findCountryLabel),\n/* harmony export */   findIndustryByLargeIcon: () => (/* binding */ findIndustryByLargeIcon),\n/* harmony export */   findIndustryClassname: () => (/* binding */ findIndustryClassname),\n/* harmony export */   findIndustryColoredIcon: () => (/* binding */ findIndustryColoredIcon),\n/* harmony export */   findIndustryIcon: () => (/* binding */ findIndustryIcon),\n/* harmony export */   findIndustryLabel: () => (/* binding */ findIndustryLabel),\n/* harmony export */   findIndustryLink: () => (/* binding */ findIndustryLink),\n/* harmony export */   findIndustryLogoSvg: () => (/* binding */ findIndustryLogoSvg),\n/* harmony export */   findnotificationColoredIcon: () => (/* binding */ findnotificationColoredIcon),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateArticle: () => (/* binding */ formatDateArticle),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatResumeName: () => (/* binding */ formatResumeName),\n/* harmony export */   generateLocalizedSlug: () => (/* binding */ generateLocalizedSlug),\n/* harmony export */   getCountryEventImage: () => (/* binding */ getCountryEventImage),\n/* harmony export */   getCountryImage: () => (/* binding */ getCountryImage),\n/* harmony export */   getExtension: () => (/* binding */ getExtension),\n/* harmony export */   getMenuListByRole: () => (/* binding */ getMenuListByRole),\n/* harmony export */   getRoutesListByRole: () => (/* binding */ getRoutesListByRole),\n/* harmony export */   getSlugByIndustry: () => (/* binding */ getSlugByIndustry),\n/* harmony export */   highlightMatchingWords: () => (/* binding */ highlightMatchingWords),\n/* harmony export */   industryExists: () => (/* binding */ industryExists),\n/* harmony export */   isExpired: () => (/* binding */ isExpired),\n/* harmony export */   processContent: () => (/* binding */ processContent),\n/* harmony export */   splitFirstWord: () => (/* binding */ splitFirstWord),\n/* harmony export */   splitLastWord: () => (/* binding */ splitLastWord),\n/* harmony export */   stringAvatar: () => (/* binding */ stringAvatar),\n/* harmony export */   stringToColor: () => (/* binding */ stringToColor),\n/* harmony export */   truncateByCharacter: () => (/* binding */ truncateByCharacter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(middleware)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var _config_countries__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/countries */ \"(middleware)/./src/config/countries.js\");\n/* harmony import */ var _config_inustries__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/config/inustries */ \"(middleware)/./src/config/inustries.js\");\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! html-to-text */ \"(middleware)/./node_modules/html-to-text/lib/html-to-text.mjs\");\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./constants */ \"(middleware)/./src/utils/constants.js\");\n/* harmony import */ var _helpers_MenuList__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/helpers/MenuList */ \"(middleware)/./src/helpers/MenuList.js\");\n/* harmony import */ var _config_Constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/config/Constants */ \"(middleware)/./src/config/Constants.js\");\n/* harmony import */ var _helpers_routesList__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/helpers/routesList */ \"(middleware)/./src/helpers/routesList.js\");\n\n\n\n\n\n\n\n\nconst getExtension = (fileType)=>{\n    switch(fileType){\n        case \"application/pdf\":\n            return \"pdf\";\n        case \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\":\n            return \"docx\";\n        case \"image/png\":\n            return \"png\";\n        case \"image/jpg\":\n            return \"jpg\";\n        case \"image/jpeg\":\n            return \"jpeg\";\n        default:\n            return \"unknown\";\n    }\n};\n// functions.js\nfunction formatDateArticle(dateString) {\n    if (!dateString) return \"\"; // Handle empty or undefined dateString\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n        console.error(`Invalid date string: ${dateString}`);\n        return \"\";\n    }\n    const dateOptions = {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    };\n    const timeOptions = {\n        hour: \"numeric\",\n        minute: \"2-digit\",\n        hour12: true\n    };\n    const formattedDate = date.toLocaleDateString(\"en-US\", dateOptions);\n    const formattedTime = date.toLocaleTimeString(\"en-US\", timeOptions);\n    return `${formattedDate}, ${formattedTime}`;\n}\nfunction formatDate(dateString) {\n    if (!dateString) return \"\"; // Handle empty or undefined dateString\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) {\n        console.error(`Invalid date string: ${dateString}`);\n        return \"\";\n    }\n    const options = {\n        year: \"numeric\",\n        month: \"2-digit\",\n        day: \"2-digit\"\n    };\n    return date.toLocaleDateString(\"en-US\", options);\n}\nfunction formatResumeName(resume, fullName) {\n    if (typeof resume !== \"string\") {\n        console.error(\"Le nom du fichier de CV n'est pas valide.\");\n        return \"CV_Anonyme\";\n    }\n    const extension = resume.split(\".\").pop();\n    if (!extension || extension === resume) {\n        console.error(\"Le fichier n'a pas d'extension valide.\");\n        return `CV_${fullName}`;\n    }\n    return `CV_${fullName}.${extension}`;\n}\nconst processContent = (htmlContent)=>{\n    const plainTextContent = (0,html_to_text__WEBPACK_IMPORTED_MODULE_3__.htmlToText)(htmlContent, {\n        wordwrap: false\n    });\n    return plainTextContent.length > 150 ? plainTextContent.substring(0, 150) + \"...\" : plainTextContent;\n};\nconst industryExists = (text)=>{\n    const result = _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.some((item)=>item.value === text || item.pentabellValue === text);\n    // if (result == true) {\n    //   if (text == \"OTHER\") {\n    //     return false;\n    //   } else {\n    //     return true;\n    //   }\n    // }\n    return result;\n};\nconst findIndustryLogoSvg = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue === text)?.logoSvg;\n};\nconst findIndustryLabel = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue === text)?.label;\n};\nconst findIndustryIcon = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue === text)?.icon;\n};\nconst findIndustryColoredIcon = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value && text && String(item.value).toLocaleLowerCase() === String(text).toLocaleLowerCase() || item.pentabellValue === text)?.iconColored;\n};\nconst findnotificationColoredIcon = (text)=>{\n    return _config_Constants__WEBPACK_IMPORTED_MODULE_6__.Notifications_LIST.find((item)=>item.value && text && String(item.value).toLocaleLowerCase() === String(text).toLocaleLowerCase() || item.pentabellValue === text)?.iconColored;\n};\nconst findIndustryClassname = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue.toLocaleLowerCase() === text.toLocaleLowerCase())?.classname;\n};\nconst findIndustryLink = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue.toLocaleLowerCase() === text.toLocaleLowerCase())?.link;\n};\nconst findIndustryByLargeIcon = (text)=>{\n    return _config_inustries__WEBPACK_IMPORTED_MODULE_2__.INDUSTRIES_LIST.find((item)=>item.value.toLocaleLowerCase() === text.toLocaleLowerCase() || item.pentabellValue.toLocaleLowerCase() === text.toLocaleLowerCase())?.largeIcon;\n};\nconst findCountryFlag = (text)=>{\n    return _config_countries__WEBPACK_IMPORTED_MODULE_1__.COUNTRIES_LIST_FLAG.find((item)=>item.value === text || item.pentabellValue === text)?.flag;\n};\nconst findCountryLabel = (text)=>{\n    return _config_countries__WEBPACK_IMPORTED_MODULE_1__.COUNTRIES_LIST_FLAG.find((item)=>item.value === text || item.pentabellValue === text)?.label;\n};\nconst getMenuListByRole = (currentUser)=>{\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n        return _helpers_MenuList__WEBPACK_IMPORTED_MODULE_5__.MenuList?.admin;\n    }\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n        return _helpers_MenuList__WEBPACK_IMPORTED_MODULE_5__.MenuList?.candidate;\n    }\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n        return _helpers_MenuList__WEBPACK_IMPORTED_MODULE_5__.MenuList?.editor;\n    }\n};\nconst getRoutesListByRole = (currentUser)=>{\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.ADMIN)) {\n        return _helpers_routesList__WEBPACK_IMPORTED_MODULE_7__.adminPermissionsRoutes;\n    }\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.CANDIDATE)) {\n        return _helpers_routesList__WEBPACK_IMPORTED_MODULE_7__.candidatePermissionsRoutes;\n    }\n    if (currentUser?.roles?.includes(_constants__WEBPACK_IMPORTED_MODULE_4__.Role.EDITOR)) {\n        return _helpers_routesList__WEBPACK_IMPORTED_MODULE_7__.editorPermissionsRoutes;\n    }\n};\nconst getCountryImage = (country)=>{\n    const formattedCountry = country.toLowerCase().replace(/ /g, \"-\");\n    return `${\"http://localhost:4000\"}/api/v1/maps/${formattedCountry}.png`;\n};\nconst getCountryEventImage = (country)=>{\n    const formattedCountry = country.toLowerCase().replace(/ /g, \"-\");\n    return `https://www.pentabell.com/eventMaps/${formattedCountry}.svg`;\n};\nconst generateLocalizedSlug = (locale, slug)=>{\n    return locale === \"en\" ? `${slug}/` : `/fr${slug}/`;\n};\nconst capitalizeFirstLetter = (str)=>{\n    return str.split(\" \").map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(\" \");\n};\nfunction stringToColor(string) {\n    let hash = 0;\n    let i;\n    /* eslint-disable no-bitwise */ for(i = 0; i < string?.length; i += 1){\n        hash = string.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    let color = \"#\";\n    for(i = 0; i < 3; i += 1){\n        const value = hash >> i * 8 & 0xff;\n        color += `00${value.toString(16)}`.slice(-2);\n    }\n    /* eslint-enable no-bitwise */ return color;\n}\nfunction stringAvatar(name) {\n    return {\n        sx: {\n            bgcolor: stringToColor(name)\n        },\n        children: `${name?.split(\" \")[0][0]}${name?.split(\" \")[1][0]}`\n    };\n}\nconst splitFirstWord = (txt)=>{\n    const words = txt?.toString().split(\" \") || [];\n    const firstWord = words[0];\n    const restOfText = words.slice(1).join(\" \");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"first-word\",\n                children: [\n                    firstWord,\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\utils\\\\functions.js\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, undefined),\n            restOfText\n        ]\n    }, void 0, true);\n};\nconst highlightMatchingWords = (txt, wordsToHighlight)=>{\n    if (!txt) return null;\n    const regex = new RegExp(`\\\\b(${wordsToHighlight.join(\"|\")})\\\\b`, \"gi\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: txt.split(regex).map((segment, index)=>{\n            const isMatch = wordsToHighlight.includes(segment.trim());\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: isMatch ? \"last-word\" : \"\",\n                children: segment\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\utils\\\\functions.js\",\n                lineNumber: 272,\n                columnNumber: 11\n            }, undefined);\n        })\n    }, void 0, false);\n};\nconst splitLastWord = (txt)=>{\n    const words = txt?.toString().split(\" \") || [];\n    const lastWord = words[words.length - 1]; // Get the last word\n    const restOfText = words.slice(0, -1).join(\" \"); // Join all except the last word\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            restOfText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: [\n                    restOfText,\n                    \" \"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\utils\\\\functions.js\",\n                lineNumber: 286,\n                columnNumber: 22\n            }, undefined),\n            \" \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"last-word\",\n                children: lastWord\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pentabellversion2.0\\\\client\\\\src\\\\utils\\\\functions.js\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst formatDuration = (receivedTime)=>{\n    const duration = moment.duration(moment().diff(moment(receivedTime)));\n    if (duration.asDays() >= 1) {\n        return `${Math.floor(duration.asDays())}j`;\n    } else if (duration.asHours() >= 1) {\n        return `${Math.floor(duration.asHours())}h`;\n    } else {\n        return `${Math.floor(duration.minutes())}min`;\n    }\n};\nconst isExpired = (dateOfExpiration)=>{\n    const currentDate = new Date();\n    let expirationDate = new Date(currentDate);\n    if (dateOfExpiration) expirationDate = new Date(dateOfExpiration);\n    else expirationDate.setMonth(expirationDate.getMonth() + 3);\n    return expirationDate < currentDate;\n};\nfunction truncateByCharacter(text, maxChars) {\n    if (text?.length <= maxChars) return text;\n    return text?.slice(0, maxChars).trim() + \"…\";\n}\nconst getSlugByIndustry = (industry)=>{\n    const industryValue = industry || \"\";\n    switch(industryValue){\n        case \"Energies\":\n            return \"energies\";\n        case \"It & Telecom\":\n            return \"it-telecom\";\n        case \"Banking\":\n            return \"banking-insurance\";\n        case \"Transport\":\n            return \"transport\";\n        case \"Pharmaceutical\":\n            return \"pharmaceutical\";\n        default:\n            return \"other\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/utils/functions.js\n");

/***/ })

});