import banner from "@/assets/images/iraq/iraqBanner.webp";
import BannerComponents from "@/components/sections/BannerComponents";
import OurPartners from "@/components/sections/OurPartners";
import ResponsiveRowTitleText from "@/components/ui/ResponsiveRowTitleText";
import r1 from "@/assets/images/services/recrutement1.png";
import r2 from "@/assets/images/services/recrutement2.png";
import r3 from "@/assets/images/services/recrutement3.png";
import r4 from "@/assets/images/services/recrutement4.png";
import serviceImg from "@/assets/images/services/service1.png";
import TunisiaOfficePageForm from "@/features/forms/components/TunisiaOfficePageForm";
import GlobalHRServicesSection from "@/components/sections/GlobalHRServicesSection";
import initTranslations from "@/app/i18n";
import ComplexityControlSectionQatar from "../../../../components/offices-sections/qatar/ComplexityControlSectionQatar";
import QatarLaborLaws from "../../../../components/labor-laws/QatarLaborLaws";
import { websiteRoutesList } from "@/helpers/routesList";
import { axiosGetJsonSSR } from "@/config/axios";
import OfficeInfoIraq from "../../../../components/offices-sections/iraq/OfficeInfoIraq";
import BusinessInIraq from "../../../../components/offices-sections/iraq/BusinessInIraq";
import OfficeLocationMapIraq from "../../../../components/offices-sections/iraq/OfficeLocationMapIraq";
import ComplexityControlSectionIraq from "../../../../components/offices-sections/iraq/ComplexityControlSectionIraq";
import IraqLaborLaws from "../../../../components/labor-laws/IraqLaborLaws";
import serviceimgS1 from "@/assets/images/services/service1.png";

export async function generateMetadata({ params: { locale } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }international-hr-services-recruitment-agency-iraq/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/international-hr-services-recruitment-agency-iraq/`,
    en: `https://www.pentabell.com/international-hr-services-recruitment-agency-iraq/`,
    "x-default": `https://www.pentabell.com/international-hr-services-recruitment-agency-iraq/`,
  };

  const { t } = await initTranslations(locale, ["servicesByCountry"]);
  try {
    const res = await axiosGetJsonSSR.get(
      `${process.env.NEXT_PUBLIC_BASE_API_URL_SSR}/seoTags/${locale}/international-hr-services-recruitment-agency-iraq`
    );
    const seoTags = res?.data?.status === 200;
    if (seoTags) {
      return {
        title: res?.data?.data?.versions[0]?.metaTitle,
        description: res?.data?.data?.versions[0]?.metaDescription,
        robots: res?.data?.data?.robotMeta,
        alternates: {
          canonical: canonicalUrl,
          languages,
        },
      };
    }
  } catch (error) {
    console.error("Error fetching SEO tags:", error);
  }
  return {
    title: t("servicesByCountry:iraq:metaTitle"),
    description: t("servicesByCountry:iraq:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
    robots: "follow, index, max-snippet:-1, max-image-preview:large",
  };
}
async function hiringEmployeesSaudiGuide({ params: { locale } }) {
  const { t } = await initTranslations(locale, ["Tunisia", "iraq"]);

  const SERVICES = [
    {
      id: "s1",
      title: t("Tunisia:services:dataS1:title"),
      description: t("Tunisia:services:dataS1:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.payrollServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: serviceImg,
      altImg: t("Tunisia:services:dataS1:altImg"),
    },
    {
      id: "s2",
      title: t("Tunisia:services:dataS2:title"),
      description: t("Tunisia:services:dataS2:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.consultingServices.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r4,
      altImg: t("Tunisia:services:dataS2:altImg"),
    },
    {
      id: "s3",
      title: t("Tunisia:services:dataS3:title"),
      description: t("Tunisia:services:dataS3:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.technicalAssistance.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r3,
      altImg: t("Tunisia:services:dataS3:altImg"),
    },
    {
      id: "s4",
      title: t("Tunisia:services:dataS4:title"),
      description: t("Tunisia:services:dataS4:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.aiSourcing.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r2,
      altImg: t("Tunisia:services:dataS4:altImg"),
    },
    {
      id: "s5",
      title: t("Tunisia:services:dataS5:title"),
      description: t("Tunisia:services:dataS5:description"),
      link: `/${websiteRoutesList.services.route}/${websiteRoutesList.directHiring.route}`,
      linkText: t("Tunisia:services:linkText"),
      img: r1,
      altImg: t("Tunisia:services:dataS5:altImg"),
    },
  ];
  return (
    <div>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Organization",
            name: t("contactUs:bureux:contacts:tunisia"),
            address: {
              "@type": "PostalAddress",
              streetAddress: "Imm. MADIBA, Rue Khawarizmi",
              addressLocality: "La Goulette",
              postalCode: "2015",
              addressCountry: "TN",
            },
            telephone: ["+216 31 385 510"],
            email: "<EMAIL>",
            url:
              locale === "en"
                ? "https://www.pentabell.com/hiring-employees-tunisia-guide/"
                : `https://www.pentabell.com/${locale}/hiring-employees-tunisia-guide/`,
          }),
        }}
      />
      <BannerComponents
        title={t("iraq:title")}
        description={t("iraq:description")}
        bannerImg={banner}
        height={"100vh"}
        altImg={t("iraq:altImg")}
      />
      <OurPartners disableTxt={true} />
      <ResponsiveRowTitleText
        title={t("iraq:intro:title")}
        paragraph={t("iraq:intro:description")}
        paragraph2={t("iraq:intro:description2")}
      />
      <OfficeInfoIraq t={t} />
      <BusinessInIraq t={t} />

      <OfficeLocationMapIraq t={t} />

      <ComplexityControlSectionIraq t={t} />

      <GlobalHRServicesSection
        title={t("iraq:services:title")}
        SERVICES={SERVICES}
        defaultImage={serviceimgS1}
      />
      <IraqLaborLaws />
      <TunisiaOfficePageForm country={"Iraq"} defaultCountryPhone="iq" />
    </div>
  );
}

export default hiringEmployeesSaudiGuide;
