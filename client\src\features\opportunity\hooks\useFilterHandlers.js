import { useCallback, useEffect } from "react";
import { useMediaQuery, useTheme } from "@mui/material";

const useFilterHandlers = ({
  setFieldValue,
  values,
  pathname,
  setPageNumber,
  setSelectedFilters,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery(theme.breakpoints.down("md"));
  useEffect(() => {
    const handleFiltersReset = (event) => {
      const preserveIndustry = event.detail?.preserveIndustry;
      const preserveCountry = event.detail?.preserveCountry;

      setFieldValue("jobDescriptionLanguages", []);
      setFieldValue("levelOfExperience", []);
      setFieldValue("contractType", []);
      setFieldValue("keyWord", []);

      if (!preserveIndustry) {
        setFieldValue("industry", []);
      }

      if (!preserveCountry) {
        setFieldValue("country", []);
      }
    };

    window.addEventListener("filtersReset", handleFiltersReset);

    return () => {
      window.removeEventListener("filtersReset", handleFiltersReset);
    };
  }, [setFieldValue]);

  const updateUrlAndDispatchEvent = useCallback(
    (newParams) => {
      const scrollPosition =
        window.scrollY || document.documentElement.scrollTop;

      const currentParams = new URLSearchParams(window.location.search);
      if (currentParams.has("list") && !newParams.has("list")) {
        newParams.set("list", currentParams.get("list"));
      }

      const newUrl = `${pathname}?${newParams.toString()}`;
      window.history.replaceState({ path: newUrl }, "", newUrl);

      const paramsObject = {};
      for (const [key, value] of newParams.entries()) {
        paramsObject[key] = value;
      }

      window.dispatchEvent(
        new CustomEvent("filterChanged", {
          detail: {
            params: paramsObject,
            maintainScroll: true,
            scrollPosition: scrollPosition,
          },
        })
      );

      window.scrollTo({
        top: scrollPosition,
        behavior: "instant",
      });
    },
    [pathname]
  );

  const handleCheckboxChange = useCallback(
    (category, value) => {
      const isRemoving = values[category]?.includes(value);
      const newValues = isRemoving
        ? values[category]?.filter((item) => item !== value)
        : [...(values[category] || []), value];

      setFieldValue(category, newValues);

      const newParams = new URLSearchParams(window.location.search);

      if (newValues.length > 0) {
        newParams.set(category, newValues.join(","));
      } else {
        newParams.delete(category);
      }

      const currentParams = new URLSearchParams(window.location.search);
      if (currentParams.has("list") && !newParams.has("list")) {
        newParams.set("list", currentParams.get("list"));
      }

      newParams.set("pageNumber", 1);
      setPageNumber(1);

      updateUrlAndDispatchEvent(newParams);

      setSelectedFilters((prev) => {
        const filtered = prev.filter((f) => f.category !== category);
        if (newValues.length > 0) {
          const updatedFilters = [
            ...filtered,
            ...newValues.map((item) => ({ category, label: item })),
          ];
          return updatedFilters;
        }

        return filtered;
      });

      window.dispatchEvent(
        new CustomEvent("checkboxFilterChanged", {
          detail: {
            category,
            value,
            newValues,
            allValues: values,
            maintainScroll: true,
            scrollPosition:
              window.scrollY || document.documentElement.scrollTop,
          },
        })
      );
    },
    [
      values,
      setFieldValue,
      setPageNumber,
      setSelectedFilters,
      updateUrlAndDispatchEvent,
    ]
  );

  const handleSearchChange = useCallback(
    (e) => {
      const inputValue = e.target.value;
      const newValue = inputValue ? [inputValue] : [];

      setFieldValue("keyWord", newValue);

      if (isMobile || isTablet) {
        const newParams = new URLSearchParams(window.location.search);

        if (newValue.length > 0 && newValue[0].trim()) {
          newParams.set("keyWord", newValue[0]);
        } else {
          newParams.delete("keyWord");
        }

        const currentParams = new URLSearchParams(window.location.search);
        if (currentParams.has("list") && !newParams.has("list")) {
          newParams.set("list", currentParams.get("list"));
        }

        newParams.set("pageNumber", 1);
        setPageNumber(1);

        updateUrlAndDispatchEvent(newParams);

        setSelectedFilters((prev) => {
          const filtered = prev.filter((f) => f.category !== "keyWord");
          if (newValue.length > 0 && newValue[0].trim()) {
            return [...filtered, { category: "keyWord", label: newValue[0] }];
          }
          return filtered;
        });

        window.dispatchEvent(
          new CustomEvent("checkboxFilterChanged", {
            detail: {
              category: "keyWord",
              value: newValue[0] || "",
              newValues: newValue,
              allValues: values,
              maintainScroll: true,
              scrollPosition:
                window.scrollY || document.documentElement.scrollTop,
            },
          })
        );
      }
    },
    [
      setFieldValue,
      isMobile,
      isTablet,
      setPageNumber,
      setSelectedFilters,
      updateUrlAndDispatchEvent,
      values,
    ]
  );

  const handleCountryChange = useCallback(
    (_, newValue) => {
      setFieldValue("country", newValue);

      if (isMobile || isTablet) {
        const newParams = new URLSearchParams(window.location.search);

        if (newValue && newValue.trim()) {
          newParams.set("country", newValue);
        } else {
          newParams.delete("country");
        }

        const currentParams = new URLSearchParams(window.location.search);
        if (currentParams.has("list") && !newParams.has("list")) {
          newParams.set("list", currentParams.get("list"));
        }

        newParams.set("pageNumber", 1);
        setPageNumber(1);

        updateUrlAndDispatchEvent(newParams);

        setSelectedFilters((prev) => {
          const filtered = prev.filter((f) => f.category !== "country");
          if (newValue && newValue.trim()) {
            return [...filtered, { category: "country", label: newValue }];
          }
          return filtered;
        });

        window.dispatchEvent(
          new CustomEvent("checkboxFilterChanged", {
            detail: {
              category: "country",
              value: newValue || "",
              newValues: newValue ? [newValue] : [],
              allValues: values,
              maintainScroll: true,
              scrollPosition:
                window.scrollY || document.documentElement.scrollTop,
            },
          })
        );
      }
    },
    [
      setFieldValue,
      isMobile,
      isTablet,
      setPageNumber,
      setSelectedFilters,
      updateUrlAndDispatchEvent,
      values,
    ]
  );

  const handleClearFilters = useCallback(
    (e) => {
      if (e && e.preventDefault) {
        e.preventDefault();
      }

      const scrollPosition =
        window.scrollY || document.documentElement.scrollTop;

      window._isClearing = true;

      setFieldValue("jobDescriptionLanguages", []);
      setFieldValue("levelOfExperience", []);
      setFieldValue("industry", []);
      setFieldValue("contractType", []);

      if (isMobile || isTablet) {
        setFieldValue("keyWord", []);
        setFieldValue("country", []);
      }

      const currentParams = new URLSearchParams(window.location.search);

      currentParams.delete("jobDescriptionLanguages");
      currentParams.delete("levelOfExperience");
      currentParams.delete("industry");
      currentParams.delete("contractType");

      if (isMobile || isTablet) {
        currentParams.delete("keyWord");
        currentParams.delete("country");
      }

      const newUrl = `${pathname}?${currentParams.toString()}`;
      window.history.replaceState({ path: newUrl }, "", newUrl);

      const params = {};
      for (const [key, value] of currentParams.entries()) {
        params[key] = value;
      }

      window.dispatchEvent(
        new CustomEvent("filterChanged", {
          detail: {
            params: params,
            maintainScroll: true,
            scrollPosition: scrollPosition,
          },
        })
      );

      setSelectedFilters((prev) => {
        if (isMobile || isTablet) {
          return [];
        }
        return prev.filter(
          (filter) =>
            filter.category === "keyWord" || filter.category === "country"
        );
      });

      window.scrollTo({
        top: scrollPosition,
        behavior: "instant",
      });

      window._isClearing = false;

      return false;
    },
    [setFieldValue, setSelectedFilters, pathname, isMobile, isTablet]
  );

  return {
    handleCheckboxChange,
    handleSearchChange,
    handleCountryChange,
    handleClearFilters,
  };
};

export default useFilterHandlers;
