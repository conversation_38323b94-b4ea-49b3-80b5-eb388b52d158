{"c": ["app/[locale]/(website)/page", "app/[locale]/(website)/layout", "app/layout", "app/not-found", "app/[locale]/(website)/login/page", "app/[locale]/(dashboard)/backoffice/home/<USER>", "app/[locale]/(dashboard)/layout", "app/[locale]/(dashboard)/backoffice/my-profile/page", "app/[locale]/(dashboard)/backoffice/statistics/page", "app/[locale]/(dashboard)/backoffice/blogs/page", "app/[locale]/(website)/logout/page", "app/[locale]/(dashboard)/backoffice/download-report/page", "app/[locale]/(dashboard)/backoffice/sliders/page", "webpack", "_app-pages-browser_src_locales_en_Egypte_json", "_app-pages-browser_src_locales_en_createArticle_json", "_app-pages-browser_src_locales_en_dubai_json", "_app-pages-browser_src_locales_en_global_json", "_app-pages-browser_src_locales_en_morocco_json", "_app-pages-browser_src_locales_en_validations_json", "_app-pages-browser_src_locales_fr_Egypte_json", "_app-pages-browser_src_locales_fr_createArticle_json", "_app-pages-browser_src_locales_fr_dubai_json", "_app-pages-browser_src_locales_fr_global_json", "_app-pages-browser_src_locales_fr_morocco_json", "_app-pages-browser_src_locales_fr_validations_json"], "r": ["app/[locale]/(dashboard)/backoffice/download-report/page", "_app-pages-browser_src_locales_en_createGlossary_json", "_app-pages-browser_src_locales_en_glossary_json", "_app-pages-browser_src_locales_en_listGlossary_json", "_app-pages-browser_src_locales_fr_createGlossary_json", "_app-pages-browser_src_locales_fr_glossary_json", "_app-pages-browser_src_locales_fr_listGlossary_json"], "m": ["(app-pages-browser)/./src/assets/images/icons/glossaries-icon.svg", "(app-pages-browser)/./src/features/stats/charts/ApplicationsByStatus.jsx", "(app-pages-browser)/./src/features/stats/charts/ArticlesByVisibility.jsx", "(app-pages-browser)/./src/features/stats/charts/CommentByCategory.jsx", "(app-pages-browser)/./src/features/stats/charts/OpportunititesType.jsx", "(app-pages-browser)/./src/features/stats/charts/PlateformActivities.jsx", "(app-pages-browser)/./src/features/stats/charts/UsersActivities.jsx", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Caziz%5C%5CDesktop%5C%5Cpentabellversion2.0%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5C%5Blocale%5D%5C%5C(dashboard)%5C%5Cbackoffice%5C%5Cdownload-report%5C%5Cpage.jsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/[locale]/(dashboard)/backoffice/download-report/page.jsx", "(app-pages-browser)/./src/features/downloadReport/components/ListDownloadsReport.jsx", "(app-pages-browser)/./src/features/downloadReport/hooks/downloadreport.hooks.jsx", "(app-pages-browser)/./src/features/downloadReport/services/downloadreport.services.jsx", "(app-pages-browser)/./src/locales/en/createGlossary.json", "(app-pages-browser)/./src/locales/en/glossary.json", "(app-pages-browser)/./src/locales/en/listGlossary.json", "(app-pages-browser)/./src/locales/fr/createGlossary.json", "(app-pages-browser)/./src/locales/fr/glossary.json", "(app-pages-browser)/./src/locales/fr/listGlossary.json"]}