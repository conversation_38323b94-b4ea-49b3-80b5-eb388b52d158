"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1177],{93214:function(e,t,s){s.d(t,{cU:function(){return o},xk:function(){return i},yX:function(){return n}});var r=s(83464),a=s(40257);let n=r.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),i=r.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL_SSR,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0,credentials:"include"}),o=r.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},credentials:"include"});r.<PERSON><PERSON>create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0,credentials:"include"}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e))},17828:function(e,t,s){s.d(t,{Z:function(){return c}});var r=s(83464),a=s(40257),n=r.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"application/json",Accept:"application/json"},withCredentials:!0});r.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,headers:{"Content-Type":"multipart/form-data"},withCredentials:!0}),r.Z.create({baseURL:a.env.NEXT_PUBLIC_BASE_API_URL,withCredentials:!0}).interceptors.request.use(e=>(e.responseType="blob",e),e=>Promise.reject(e));var i=s(40257);let o=()=>new Promise(async(e,t)=>{try{let t=await n.get(`${i.env.NEXT_PUBLIC_BASE_API_URL}/account`);e(t.data)}catch(e){t(e)}});var u=s(86484);function c(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],{data:t,error:s,isLoading:r,refetch:a}=(0,u.useQuery)({queryKey:["currentUser"],queryFn:o,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,retry:e});return{user:t,error:s,isLoading:r,refetch:a}}},51177:function(e,t,s){s.d(t,{px:function(){return E},V3:function(){return U},kw:function(){return R},PU:function(){return P},ZC:function(){return I},iQ:function(){return g},vD:function(){return A},NB:function(){return w},fV:function(){return C}});var r=s(86484),a=s(7261),n=s(93214),i=s(4174),o=s(46172);let u=e=>{let t=e.t;return new Promise(async(s,r)=>{n.yX.put(`${o.Y.updateUser}/${e.user}`,e.nonEmptyValues).then(t=>{e.nonEmptyValues.profilePicture||a.Am.success("Personal information updated successfully."),t.data&&s(t.data)}).catch(e=>{e&&e.response&&e.response.data&&404===e.response.status&&a.Am.error(t("messages:userNotFound")),e&&r(e)})})},c=e=>new Promise(async(t,s)=>{n.yX.put(`${o.Y.users}/archive/${e.id}`,{archive:e.archive}).then(e=>{a.Am.success("user updated successfully"),e?.data&&t(e.data)}).catch(e=>{e&&e.response&&e.response.data,e&&s(e)})}),d=e=>new Promise(async(t,s)=>{try{let s=await n.xk.get(`/users/${e}`);t(s.data)}catch(e){s(e)}}),l=e=>{let t=e.body.t;return new Promise(async(s,r)=>{n.yX.put(`${o.Y.candidate}`,e.body).then(e=>{e.data&&(s(e.data),a.Am.success(t("messages:professionalInfoUpdated")))}).catch(e=>{e&&e.response&&e.response.data&&404===e.response.status&&a.Am.error(t("messages:candidateNotFound")),e&&r(e)})})},p=e=>new Promise(async(e,t)=>{try{let t=await n.yX.get(o.Y.currentUser);e(t.data)}catch(e){e.response&&404===e.response.status&&a.Am.error(i.X.USER_NOT_FOUND),e.response&&401===e.response.status||a.Am.error(i.X.SERVER_ERROR),t(e)}}),y=e=>new Promise(async(t,s)=>{try{let s=await n.yX.get(`${o.Y.users}/${e}`);t(s.data)}catch(e){e.response&&404===e.response.status&&a.Am.error(i.X.USER_NOT_FOUND),e.response&&401===e.response.status||a.Am.error(i.X.SERVER_ERROR),s(e)}}),m=()=>new Promise(async(e,t)=>{try{let t=await n.yX.get(`${o.Y.candidate}/currentCandidate`);e(t.data)}catch(e){e.response&&404===e.response.status&&a.Am.error(i.X.CandidateNotFound),t(e)}}),_=(e,t)=>new Promise(async(s,r)=>{try{let r=e.includes(t)?e:[...e,t],a=await n.yX.get(`${o.Y.skills}?industries=${encodeURIComponent(r.length>0?r.join(","):[])}`);s(a.data)}catch(e){r(e)}});async function h(e){let t=e.t;try{let s=await n.yX.put(`${o.v}/account/password`,e.passwordData);return a.Am.success(t("settings:supdatepassword")),s.data}catch(e){throw e.response&&406===e.response.status?a.Am.error(t("messages:currentpassword")):a.Am.error(t("messages:errorupdatepassword")),e.response?e.response.data:e}}var f=s(17828);let A=()=>{let e=(0,r.useQueryClient)(),{refetch:t}=g();return(0,r.useMutation)({mutationFn:e=>u(e),onSuccess:s=>{e.invalidateQueries("userData"),t(),localStorage.setItem("user",JSON.stringify(s))},onError:e=>{e.message=""}})},E=()=>(0,r.useMutation)({mutationFn:e=>{let{id:t,archive:s}=e;return c({id:t,archive:s})},onError:e=>{e.message="error on useArchiveduser"}}),w=()=>{let e=(0,r.useQueryClient)(),{user:t}=(0,f.Z)();return(0,r.useMutation)({mutationFn:e=>l({body:e}),onSuccess:t=>{e.invalidateQueries("candidateData")},onError:e=>{e.message=""}})},P=e=>(0,r.useQuery)(["user",e],async()=>await d(e)),g=()=>(0,r.useQuery)("userData",async()=>{try{return await p()}catch(e){}}),I=e=>(0,r.useQuery)("userData",async()=>{try{return await y(e)}catch(e){}}),U=()=>{let{user:e}=(0,f.Z)();return(0,r.useQuery)(["getCandidateData"],async()=>{try{return await m()}catch(e){}})},R=(e,t)=>(0,r.useQuery)("skills",async()=>{try{return await _(e,t)}catch(e){}}),C=()=>{let e=(0,r.useQueryClient)();return(0,r.useMutation)(h,{onSuccess:()=>{e.invalidateQueries("password")}})}},4174:function(e,t,s){s.d(t,{X:function(){return r}});let r={INCORRECT_PASSWORD:"Password incorrect",EMAIL_NOT_FOUND:"There's no user with this email",InvalidEmail:"Invalid email address.",FailedUpdateFile:"failed to upload file try again !!",ACCOUNT_NOTACTIVATED:"Your account is not activated yet. Please check your email for the activation link.",EMAIL_EXIST:"Email already exist",FileExist:"File already exists",FileNotFound:"File not found!",error:"'An unknown error occurred'",ERROR:"An error occurred. Please try again later !",INVALID_SIGNUP_DATA:"Invalid signup data. Please check your information and try again.",CandidateNotFound:"Candidate not found",ResetPasswordLink:"Check your email. Link expires in 10 mins!",VALIDATIONS:{INVALID_EMAIL:"Invalid email",EMPTY_FIELD:"Please fill in the required fields!",END_DATE:"End date must be after start date",MIN_DATE:"Date of birth must be after 1950",MAX_DATE:"Date must be before 2005",MIN_LENGTH:"Field must be at least 3 characters",MAX_LENGTH:"Field must be at most 20 characters",REQUIRED:"This field is required!",INVALID_PASSWORD:"Password requires at least one uppercase, one lowercase letter, and one digit",PASSWORDMATCH:"password must match"}}},46172:function(e,t,s){s.d(t,{Y:function(){return a},v:function(){return r}});let r=s(40257).env.NEXT_PUBLIC_BASE_API_URL,a={seo:"seoTags",auth:"/auth/signin",logout:"/auth/logout",candidatures:"/applications",signup:"/auth/signup",forgetPassword:"/auth/forgot-password",resetPassword:"/auth/reset-password",guides:"/guides",currentUser:"/users/current",updateUser:"/users",users:"/users",categoryGuides:"guidecategory",candidate:"/candidates",report:"/report",skills:"/skills",files:"/files",applications:"/applications",sliders:"/sliders",favoris:"/candidate/favourite",articles:"/articles",categories:"/categories",blog:"/blog",category:"/categories",opportunity:"/opportunities",seoOpportunity:"/seoOpportunity",newsletter:"/newsletter",contact:"/contact",favourite:"/favourite",contacts:"contacts",comments:"/comments",statistics:"/statistics",events:"/events",baseUrl:`${r}`}}}]);