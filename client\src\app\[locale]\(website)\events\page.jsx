import initTranslations from "@/app/i18n";
import BannerComponents from "@/components/sections/BannerComponents";
import bannerEvents from "@/assets/images/bannerEvents.webp";
import EventsList from "@/components/events/EventsList";
import { Visibility } from "@/utils/constants";
import { axiosGetJsonSSR } from "@/config/axios";

export async function generateMetadata({ params: { locale, industry } }) {
  const canonicalUrl = `https://www.pentabell.com/${
    locale !== "en" ? `${locale}/` : ""
  }events/`;

  const languages = {
    fr: `https://www.pentabell.com/fr/events/`,
    en: `https://www.pentabell.com/events/`,
    "x-default": `https://www.pentabell.com/events/`,
  };

  const { t } = await initTranslations(locale, ["event", "global"]);

  return {
    title: t("event:metaTitle"),
    description: t("event:metaDescription"),
    alternates: {
      canonical: canonicalUrl,
      languages,
    },
  };
}

const page = async ({ searchParams, params }) => {
  const { t } = await initTranslations(params.locale, ["event", "global"]);

  const res = await axiosGetJsonSSR.get(`/events`, {
    params: {
      language: params.locale,
      visibility: "Public",
      pageSize: 9,
      pageNumber: searchParams?.pageNumber || 1,
      searchQuery: searchParams?.keyword || "",
    },
  });

  const events = res?.data?.Events;

  return (
    <div id="events-page">
      <BannerComponents
        language={params.locale}
        bannerImg={bannerEvents}
        title={t("events:title1")}
        titleHighlight={t("events:titleHighlight")}
        title2={t("events:title2")}
        subtitle={t("events:subTitle")}
        centerValue={true}
        isEvent={true}
        height={"70vh"}
      />
      <EventsList eventList={events} t={t} language={params.locale} />
    </div>
  );
};
export default page;
